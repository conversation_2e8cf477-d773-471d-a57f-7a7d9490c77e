


ARM Macro Assembler    Page 1 


    1 00000000         ;/*!
    2 00000000         ;    \file    startup_gd32f3x0.s
    3 00000000         ;    \brief   start up file
    4 00000000         ;
    5 00000000         ;    \version 2025-01-01, V2.5.0, firmware for GD32F3x0
    6 00000000         ;*/
    7 00000000         
    8 00000000         ;/*  Copyright (c) 2011 - 2012 ARM LIMITED
    9 00000000         ;    Copyright (c) 2025, GigaDevice Semiconductor Inc.
   10 00000000         ;
   11 00000000         ;    All rights reserved.
   12 00000000         ;    Redistribution and use in source and binary forms, 
                       with or without
   13 00000000         ;    modification, are permitted provided that the follo
                       wing conditions are met:
   14 00000000         ;    - Redistributions of source code must retain the ab
                       ove copyright
   15 00000000         ;      notice, this list of conditions and the following
                        disclaimer.
   16 00000000         ;    - Redistributions in binary form must reproduce the
                        above copyright
   17 00000000         ;      notice, this list of conditions and the following
                        disclaimer in the
   18 00000000         ;      documentation and/or other materials provided wit
                       h the distribution.
   19 00000000         ;    - Neither the name of ARM nor the names of its cont
                       ributors may be used
   20 00000000         ;      to endorse or promote products derived from this 
                       software without
   21 00000000         ;      specific prior written permission.
   22 00000000         ;    *
   23 00000000         ;    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS 
                       AND CONTRIBUTORS "AS IS"
   24 00000000         ;    AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, B
                       UT NOT LIMITED TO, THE
   25 00000000         ;    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS F
                       OR A PARTICULAR PURPOSE
   26 00000000         ;    ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS
                        AND CONTRIBUTORS BE
   27 00000000         ;    LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIA
                       L, EXEMPLARY, OR
   28 00000000         ;    CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED T
                       O, PROCUREMENT OF
   29 00000000         ;    SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
                        PROFITS; OR BUSINESS
   30 00000000         ;    INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF L
                       IABILITY, WHETHER IN
   31 00000000         ;    CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGL
                       IGENCE OR OTHERWISE)
   32 00000000         ;    ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
                        EVEN IF ADVISED OF THE
   33 00000000         ;    POSSIBILITY OF SUCH DAMAGE.
   34 00000000         ;*/
   35 00000000         
   36 00000000         ; <h> Stack Configuration
   37 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   38 00000000         ; </h>
   39 00000000         
   40 00000000 00000400 



ARM Macro Assembler    Page 2 


                       Stack_Size
                               EQU              0x00000400
   41 00000000         
   42 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   43 00000000         Stack_Mem
                               SPACE            Stack_Size
   44 00000400         __initial_sp
   45 00000400         
   46 00000400         
   47 00000400         ; <h> Heap Configuration
   48 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   49 00000400         ; </h>
   50 00000400         
   51 00000400 00000400 
                       Heap_Size
                               EQU              0x00000400
   52 00000400         
   53 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   54 00000000         __heap_base
   55 00000000         Heap_Mem
                               SPACE            Heap_Size
   56 00000400         __heap_limit
   57 00000400         
   58 00000400                 PRESERVE8
   59 00000400                 THUMB
   60 00000400         
   61 00000400         ;               /* reset Vector Mapped to at Address 0 *
                       /
   62 00000400                 AREA             RESET, DATA, READONLY
   63 00000000                 EXPORT           __Vectors
   64 00000000                 EXPORT           __Vectors_End
   65 00000000                 EXPORT           __Vectors_Size
   66 00000000         
   67 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   68 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   69 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   70 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   71 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   72 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   73 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   74 0000001C 00000000        DCD              0           ; Reserved
   75 00000020 00000000        DCD              0           ; Reserved
   76 00000024 00000000        DCD              0           ; Reserved
   77 00000028 00000000        DCD              0           ; Reserved
   78 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   79 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   80 00000034 00000000        DCD              0           ; Reserved
   81 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler



ARM Macro Assembler    Page 3 


                                                            
   82 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   83 00000040         
   84 00000040         ;               /* external interrupts handler */
   85 00000040 00000000        DCD              WWDGT_IRQHandler ; 16:Window Wa
                                                            tchdog Timer
   86 00000044 00000000        DCD              LVD_IRQHandler ; 17:LVD through
                                                             EXTI Line detect
   87 00000048 00000000        DCD              RTC_IRQHandler ; 18:RTC through
                                                             EXTI Line
   88 0000004C 00000000        DCD              FMC_IRQHandler ; 19:FMC
   89 00000050 00000000        DCD              RCU_CTC_IRQHandler 
                                                            ; 20:RCU and CTC
   90 00000054 00000000        DCD              EXTI0_1_IRQHandler ; 21:EXTI Li
                                                            ne 0 and EXTI Line 
                                                            1
   91 00000058 00000000        DCD              EXTI2_3_IRQHandler ; 22:EXTI Li
                                                            ne 2 and EXTI Line 
                                                            3
   92 0000005C 00000000        DCD              EXTI4_15_IRQHandler ; 23:EXTI L
                                                            ine 4 to EXTI Line 
                                                            15
   93 00000060 00000000        DCD              TSI_IRQHandler ; 24:TSI
   94 00000064 00000000        DCD              DMA_Channel0_IRQHandler 
                                                            ; 25:DMA Channel 0 
                                                            
   95 00000068 00000000        DCD              DMA_Channel1_2_IRQHandler ; 26:
                                                            DMA Channel 1 and D
                                                            MA Channel 2
   96 0000006C 00000000        DCD              DMA_Channel3_4_IRQHandler ; 27:
                                                            DMA Channel 3 and D
                                                            MA Channel 4
   97 00000070 00000000        DCD              ADC_CMP_IRQHandler ; 28:ADC and
                                                             Comparator 0-1
   98 00000074 00000000        DCD              TIMER0_BRK_UP_TRG_COM_IRQHandle
r 
                                                            ; 29:TIMER0 Break,U
                                                            pdate,Trigger and C
                                                            ommutation
   99 00000078 00000000        DCD              TIMER0_Channel_IRQHandler ; 30:
                                                            TIMER0 Channel Capt
                                                            ure Compare
  100 0000007C 00000000        DCD              TIMER1_IRQHandler ; 31:TIMER1
  101 00000080 00000000        DCD              TIMER2_IRQHandler ; 32:TIMER2
  102 00000084 00000000        DCD              TIMER5_DAC_IRQHandler 
                                                            ; 33:TIMER5 and DAC
                                                            
  103 00000088 00000000        DCD              0           ; Reserved
  104 0000008C 00000000        DCD              TIMER13_IRQHandler ; 35:TIMER13
                                                            
  105 00000090 00000000        DCD              TIMER14_IRQHandler ; 36:TIMER14
                                                            
  106 00000094 00000000        DCD              TIMER15_IRQHandler ; 37:TIMER15
                                                            
  107 00000098 00000000        DCD              TIMER16_IRQHandler ; 38:TIMER16
                                                            
  108 0000009C 00000000        DCD              I2C0_EV_IRQHandler 
                                                            ; 39:I2C0 Event



ARM Macro Assembler    Page 4 


  109 000000A0 00000000        DCD              I2C1_EV_IRQHandler 
                                                            ; 40:I2C1 Event
  110 000000A4 00000000        DCD              SPI0_IRQHandler ; 41:SPI0
  111 000000A8 00000000        DCD              SPI1_IRQHandler ; 42:SPI1
  112 000000AC 00000000        DCD              USART0_IRQHandler ; 43:USART0
  113 000000B0 00000000        DCD              USART1_IRQHandler ; 44:USART1
  114 000000B4 00000000        DCD              0           ; Reserved
  115 000000B8 00000000        DCD              CEC_IRQHandler ; 46:CEC
  116 000000BC 00000000        DCD              0           ; Reserved
  117 000000C0 00000000        DCD              I2C0_ER_IRQHandler 
                                                            ; 48:I2C0 Error
  118 000000C4 00000000        DCD              0           ; Reserved
  119 000000C8 00000000        DCD              I2C1_ER_IRQHandler 
                                                            ; 50:I2C1 Error
  120 000000CC 00000000        DCD              0           ; Reserved
  121 000000D0 00000000        DCD              0           ; Reserved
  122 000000D4 00000000        DCD              0           ; Reserved
  123 000000D8 00000000        DCD              0           ; Reserved
  124 000000DC 00000000        DCD              0           ; Reserved
  125 000000E0 00000000        DCD              0           ; Reserved
  126 000000E4 00000000        DCD              0           ; Reserved
  127 000000E8 00000000        DCD              USBFS_WKUP_IRQHandler 
                                                            ; 58:USBFS Wakeup
  128 000000EC 00000000        DCD              0           ; Reserved
  129 000000F0 00000000        DCD              0           ; Reserved
  130 000000F4 00000000        DCD              0           ; Reserved
  131 000000F8 00000000        DCD              0           ; Reserved
  132 000000FC 00000000        DCD              0           ; Reserved
  133 00000100 00000000        DCD              DMA_Channel5_6_IRQHandler ; 64:
                                                            DMA Channel5 and Ch
                                                            annel6 
  134 00000104 00000000        DCD              0           ; Reserved
  135 00000108 00000000        DCD              0           ; Reserved
  136 0000010C 00000000        DCD              0           ; Reserved
  137 00000110 00000000        DCD              0           ; Reserved
  138 00000114 00000000        DCD              0           ; Reserved
  139 00000118 00000000        DCD              0           ; Reserved
  140 0000011C 00000000        DCD              0           ; Reserved
  141 00000120 00000000        DCD              0           ; Reserved
  142 00000124 00000000        DCD              0           ; Reserved
  143 00000128 00000000        DCD              0           ; Reserved
  144 0000012C 00000000        DCD              0           ; Reserved
  145 00000130 00000000        DCD              0           ; Reserved
  146 00000134 00000000        DCD              0           ; Reserved
  147 00000138 00000000        DCD              0           ; Reserved
  148 0000013C 00000000        DCD              0           ; Reserved
  149 00000140 00000000        DCD              0           ; Reserved
  150 00000144 00000000        DCD              0           ; Reserved
  151 00000148 00000000        DCD              0           ; Reserved
  152 0000014C 00000000        DCD              USBFS_IRQHandler ; 83:USBFS
  153 00000150         __Vectors_End
  154 00000150         
  155 00000150 00000150 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  156 00000150         
  157 00000150                 AREA             |.text|, CODE, READONLY
  158 00000000         
  159 00000000         ;/* reset Handler */



ARM Macro Assembler    Page 5 


  160 00000000         Reset_Handler
                               PROC
  161 00000000                 EXPORT           Reset_Handler                  
   [WEAK]
  162 00000000                 IMPORT           SystemInit
  163 00000000                 IMPORT           __main
  164 00000000 4806            LDR              R0, =SystemInit
  165 00000002 4780            BLX              R0
  166 00000004 4806            LDR              R0, =__main
  167 00000006 4700            BX               R0
  168 00000008                 ENDP
  169 00000008         
  170 00000008         ;/* dummy Exception Handlers */
  171 00000008         NMI_Handler
                               PROC
  172 00000008                 EXPORT           NMI_Handler                    
   [WEAK]
  173 00000008 E7FE            B                .
  174 0000000A                 ENDP
  176 0000000A         HardFault_Handler
                               PROC
  177 0000000A                 EXPORT           HardFault_Handler              
   [WEAK]
  178 0000000A E7FE            B                .
  179 0000000C                 ENDP
  181 0000000C         MemManage_Handler
                               PROC
  182 0000000C                 EXPORT           MemManage_Handler              
   [WEAK]
  183 0000000C E7FE            B                .
  184 0000000E                 ENDP
  186 0000000E         BusFault_Handler
                               PROC
  187 0000000E                 EXPORT           BusFault_Handler               
   [WEAK]
  188 0000000E E7FE            B                .
  189 00000010                 ENDP
  191 00000010         UsageFault_Handler
                               PROC
  192 00000010                 EXPORT           UsageFault_Handler             
   [WEAK]
  193 00000010 E7FE            B                .
  194 00000012                 ENDP
  195 00000012         SVC_Handler
                               PROC
  196 00000012                 EXPORT           SVC_Handler                    
   [WEAK]
  197 00000012 E7FE            B                .
  198 00000014                 ENDP
  200 00000014         DebugMon_Handler
                               PROC
  201 00000014                 EXPORT           DebugMon_Handler               
   [WEAK]
  202 00000014 E7FE            B                .
  203 00000016                 ENDP
  205 00000016         PendSV_Handler
                               PROC
  206 00000016                 EXPORT           PendSV_Handler                 
   [WEAK]



ARM Macro Assembler    Page 6 


  207 00000016 E7FE            B                .
  208 00000018                 ENDP
  210 00000018         SysTick_Handler
                               PROC
  211 00000018                 EXPORT           SysTick_Handler                
   [WEAK]
  212 00000018 E7FE            B                .
  213 0000001A                 ENDP
  214 0000001A         
  215 0000001A         Default_Handler
                               PROC
  216 0000001A         ;               /* external interrupts handler */
  217 0000001A                 EXPORT           WWDGT_IRQHandler               
   [WEAK]
  218 0000001A                 EXPORT           LVD_IRQHandler                 
   [WEAK]
  219 0000001A                 EXPORT           RTC_IRQHandler                 
   [WEAK]
  220 0000001A                 EXPORT           FMC_IRQHandler                 
   [WEAK]
  221 0000001A                 EXPORT           RCU_CTC_IRQHandler             
   [WEAK]
  222 0000001A                 EXPORT           EXTI0_1_IRQHandler             
   [WEAK]
  223 0000001A                 EXPORT           EXTI2_3_IRQHandler             
   [WEAK]
  224 0000001A                 EXPORT           EXTI4_15_IRQHandler            
   [WEAK]
  225 0000001A                 EXPORT           TSI_IRQHandler                 
   [WEAK]
  226 0000001A                 EXPORT           DMA_Channel0_IRQHandler        
   [WEAK]
  227 0000001A                 EXPORT           DMA_Channel1_2_IRQHandler      
   [WEAK]
  228 0000001A                 EXPORT           DMA_Channel3_4_IRQHandler      
   [WEAK]
  229 0000001A                 EXPORT           ADC_CMP_IRQHandler             
   [WEAK]
  230 0000001A                 EXPORT           TIMER0_BRK_UP_TRG_COM_IRQHandle
r  [WEAK]
  231 0000001A                 EXPORT           TIMER0_Channel_IRQHandler      
   [WEAK]
  232 0000001A                 EXPORT           TIMER1_IRQHandler              
   [WEAK]
  233 0000001A                 EXPORT           TIMER2_IRQHandler              
   [WEAK]
  234 0000001A                 EXPORT           TIMER5_DAC_IRQHandler          
   [WEAK]
  235 0000001A                 EXPORT           TIMER13_IRQHandler             
   [WEAK]
  236 0000001A                 EXPORT           TIMER14_IRQHandler             
   [WEAK]
  237 0000001A                 EXPORT           TIMER15_IRQHandler             
   [WEAK]
  238 0000001A                 EXPORT           TIMER16_IRQHandler             
   [WEAK]
  239 0000001A                 EXPORT           I2C0_EV_IRQHandler             
   [WEAK]
  240 0000001A                 EXPORT           I2C1_EV_IRQHandler             



ARM Macro Assembler    Page 7 


   [WEAK]
  241 0000001A                 EXPORT           SPI0_IRQHandler                
   [WEAK]
  242 0000001A                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  243 0000001A                 EXPORT           USART0_IRQHandler              
   [WEAK]
  244 0000001A                 EXPORT           USART1_IRQHandler              
   [WEAK]
  245 0000001A                 EXPORT           CEC_IRQHandler                 
   [WEAK]
  246 0000001A                 EXPORT           I2C0_ER_IRQHandler             
   [WEAK]
  247 0000001A                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  248 0000001A                 EXPORT           USBFS_WKUP_IRQHandler          
   [WEAK]
  249 0000001A                 EXPORT           DMA_Channel5_6_IRQHandler      
   [WEAK]
  250 0000001A                 EXPORT           USBFS_IRQHandler               
   [WEAK]
  251 0000001A         
  252 0000001A         ;/* external interrupts handler */
  253 0000001A         WWDGT_IRQHandler
  254 0000001A         LVD_IRQHandler
  255 0000001A         RTC_IRQHandler
  256 0000001A         FMC_IRQHandler
  257 0000001A         RCU_CTC_IRQHandler
  258 0000001A         EXTI0_1_IRQHandler
  259 0000001A         EXTI2_3_IRQHandler
  260 0000001A         EXTI4_15_IRQHandler
  261 0000001A         TSI_IRQHandler
  262 0000001A         DMA_Channel0_IRQHandler
  263 0000001A         DMA_Channel1_2_IRQHandler
  264 0000001A         DMA_Channel3_4_IRQHandler
  265 0000001A         ADC_CMP_IRQHandler
  266 0000001A         TIMER0_BRK_UP_TRG_COM_IRQHandler
  267 0000001A         TIMER0_Channel_IRQHandler
  268 0000001A         TIMER1_IRQHandler
  269 0000001A         TIMER2_IRQHandler
  270 0000001A         TIMER5_DAC_IRQHandler
  271 0000001A         TIMER13_IRQHandler
  272 0000001A         TIMER14_IRQHandler
  273 0000001A         TIMER15_IRQHandler
  274 0000001A         TIMER16_IRQHandler
  275 0000001A         I2C0_EV_IRQHandler
  276 0000001A         I2C1_EV_IRQHandler
  277 0000001A         SPI0_IRQHandler
  278 0000001A         SPI1_IRQHandler
  279 0000001A         USART0_IRQHandler
  280 0000001A         USART1_IRQHandler
  281 0000001A         CEC_IRQHandler
  282 0000001A         I2C0_ER_IRQHandler
  283 0000001A         I2C1_ER_IRQHandler
  284 0000001A         USBFS_WKUP_IRQHandler
  285 0000001A         DMA_Channel5_6_IRQHandler
  286 0000001A         USBFS_IRQHandler
  287 0000001A         
  288 0000001A E7FE            B                .



ARM Macro Assembler    Page 8 


  289 0000001C                 ENDP
  290 0000001C         
  291 0000001C                 ALIGN
  292 0000001C         
  293 0000001C         ; user Initial Stack & Heap
  294 0000001C         
  295 0000001C                 IF               :DEF:__MICROLIB
  296 0000001C         
  297 0000001C                 EXPORT           __initial_sp
  298 0000001C                 EXPORT           __heap_base
  299 0000001C                 EXPORT           __heap_limit
  300 0000001C         
  301 0000001C                 ELSE
  316                          ENDIF
  317 0000001C         
  318 0000001C                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp --apcs=int
erwork --depend=.\output\startup_gd32f3x0.d -o.\output\startup_gd32f3x0.o -I.\R
TE\_GD32F310 -ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include -I
D:\Keil5.27\Core\ARM\CMSIS\Include --predefine="__MICROLIB SETA 1" --predefine=
"__UVISION_VERSION SETA 527" --predefine="GD32F310 SETA 1" --list=.\list\startu
p_gd32f3x0.lst ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 42 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 43 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 44 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 67 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 297 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 53 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 55 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 54 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 298 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
Comment: __heap_base used once
__heap_limit 00000400

Symbol: __heap_limit
   Definitions
      At line 56 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 299 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 62 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 67 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 63 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 155 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

__Vectors_End 00000150

Symbol: __Vectors_End
   Definitions
      At line 153 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 64 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 155 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 157 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      None
Comment: .text unused
ADC_CMP_IRQHandler 0000001A

Symbol: ADC_CMP_IRQHandler
   Definitions
      At line 265 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 97 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 229 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 186 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 72 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 187 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

CEC_IRQHandler 0000001A

Symbol: CEC_IRQHandler
   Definitions
      At line 281 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 115 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 245 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

DMA_Channel0_IRQHandler 0000001A

Symbol: DMA_Channel0_IRQHandler
   Definitions
      At line 262 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 94 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 226 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

DMA_Channel1_2_IRQHandler 0000001A

Symbol: DMA_Channel1_2_IRQHandler
   Definitions
      At line 263 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 95 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 227 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

DMA_Channel3_4_IRQHandler 0000001A

Symbol: DMA_Channel3_4_IRQHandler
   Definitions
      At line 264 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 96 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 228 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

DMA_Channel5_6_IRQHandler 0000001A

Symbol: DMA_Channel5_6_IRQHandler
   Definitions
      At line 285 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 133 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 249 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 200 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 79 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 201 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 215 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_1_IRQHandler 0000001A

Symbol: EXTI0_1_IRQHandler
   Definitions
      At line 258 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 90 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 222 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

EXTI2_3_IRQHandler 0000001A

Symbol: EXTI2_3_IRQHandler
   Definitions
      At line 259 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 91 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 223 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

EXTI4_15_IRQHandler 0000001A

Symbol: EXTI4_15_IRQHandler
   Definitions
      At line 260 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 92 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 224 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

FMC_IRQHandler 0000001A

Symbol: FMC_IRQHandler



ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 256 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 88 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 220 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 176 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 70 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 177 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

I2C0_ER_IRQHandler 0000001A

Symbol: I2C0_ER_IRQHandler
   Definitions
      At line 282 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 117 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 246 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

I2C0_EV_IRQHandler 0000001A

Symbol: I2C0_EV_IRQHandler
   Definitions
      At line 275 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 108 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 239 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 283 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 119 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 247 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 276 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 109 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 240 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

LVD_IRQHandler 0000001A

Symbol: LVD_IRQHandler
   Definitions
      At line 254 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 86 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 218 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 181 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 71 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 182 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 171 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 69 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 172 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 205 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 81 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 206 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

RCU_CTC_IRQHandler 0000001A

Symbol: RCU_CTC_IRQHandler
   Definitions
      At line 257 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 89 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 221 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

RTC_IRQHandler 0000001A

Symbol: RTC_IRQHandler
   Definitions
      At line 255 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 87 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 219 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 160 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 68 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 161 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

SPI0_IRQHandler 0000001A

Symbol: SPI0_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 277 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 110 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 241 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 278 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 111 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 242 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 195 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 78 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 196 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 210 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 82 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 211 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TIMER0_BRK_UP_TRG_COM_IRQHandler 0000001A

Symbol: TIMER0_BRK_UP_TRG_COM_IRQHandler
   Definitions
      At line 266 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 98 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 230 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TIMER0_Channel_IRQHandler 0000001A

Symbol: TIMER0_Channel_IRQHandler
   Definitions
      At line 267 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 99 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 231 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TIMER13_IRQHandler 0000001A

Symbol: TIMER13_IRQHandler
   Definitions
      At line 271 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 104 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 235 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

TIMER14_IRQHandler 0000001A

Symbol: TIMER14_IRQHandler
   Definitions
      At line 272 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 105 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 236 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TIMER15_IRQHandler 0000001A

Symbol: TIMER15_IRQHandler
   Definitions
      At line 273 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 106 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 237 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TIMER16_IRQHandler 0000001A

Symbol: TIMER16_IRQHandler
   Definitions
      At line 274 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 107 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 238 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TIMER1_IRQHandler 0000001A

Symbol: TIMER1_IRQHandler
   Definitions
      At line 268 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 100 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 232 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TIMER2_IRQHandler 0000001A

Symbol: TIMER2_IRQHandler
   Definitions
      At line 269 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 101 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 233 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TIMER5_DAC_IRQHandler 0000001A

Symbol: TIMER5_DAC_IRQHandler
   Definitions
      At line 270 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 102 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 234 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

TSI_IRQHandler 0000001A

Symbol: TSI_IRQHandler
   Definitions
      At line 261 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 93 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 225 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

USART0_IRQHandler 0000001A

Symbol: USART0_IRQHandler
   Definitions
      At line 279 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 112 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 243 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 280 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 113 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 244 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

USBFS_IRQHandler 0000001A

Symbol: USBFS_IRQHandler
   Definitions
      At line 286 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 152 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 250 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

USBFS_WKUP_IRQHandler 0000001A

Symbol: USBFS_WKUP_IRQHandler
   Definitions
      At line 284 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 127 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 248 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 191 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 73 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 192 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

WWDGT_IRQHandler 0000001A

Symbol: WWDGT_IRQHandler
   Definitions
      At line 253 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 85 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
      At line 217 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s

46 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000400

Symbol: Heap_Size
   Definitions
      At line 51 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 55 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
Comment: Heap_Size used once
Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 40 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 43 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
Comment: Stack_Size used once
__Vectors_Size 00000150

Symbol: __Vectors_Size
   Definitions
      At line 155 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 65 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 162 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 164 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 163 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
   Uses
      At line 166 in file ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s
Comment: __main used once
2 symbols
397 symbols in table
