#include "led.h"

void GD_Led_Config(void)
{

    /* enable the LED GPIO clock */
    rcu_periph_clock_enable(RCU_GPIOB);
    /* configure led GPIO port */
    gpio_mode_set(GPIOB, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, GPIO_PIN_1 );
    gpio_output_options_set(GPIOB, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_1 );
    gpio_bit_reset(GPIOB, GPIO_PIN_1 );
	
}

void Led_Fz(void)
{
       /* turn on led1, turn off led2 */
        gpio_bit_write(GPIOB, GPIO_PIN_1, SET);
        delay_ms(1000);
        /* turn on led2, turn off led1 */
        gpio_bit_write(GPIOB, GPIO_PIN_1, RESET);
        delay_ms(1000);
}
void Led_On(void)
{
	gpio_bit_write(GPIOB, GPIO_PIN_1, SET);
	
}
void Led_Off(void)
{
	gpio_bit_write(GPIOB, GPIO_PIN_1, RESET);

}

void Led_Toggle(void)
{
	gpio_bit_toggle(GPIOB, GPIO_PIN_1);
}
