Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.angle_to_hex16) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.angle_to_hex16) refers to dmul.o(.text) for __aeabi_dmul
    main.o(i.angle_to_hex16) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.angle_to_hex16) refers to dadd.o(.text) for __aeabi_dadd
    main.o(i.angle_to_hex16) refers to dfixui.o(.text) for __aeabi_d2uiz
    main.o(i.calculate_elevation_angle_by_intensity) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.calculate_elevation_angle_by_intensity) refers to acos.o(i.__hardfp_acos) for __hardfp_acos
    main.o(i.calculate_elevation_angle_by_intensity) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.calculate_elevation_angle_by_intensity) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.calculate_elevation_angle_improved) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.calculate_elevation_angle_improved) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    main.o(i.calculate_elevation_angle_improved) refers to dadd.o(.text) for __aeabi_drsub
    main.o(i.calculate_elevation_angle_improved) refers to dmul.o(.text) for __aeabi_dmul
    main.o(i.calculate_elevation_angle_improved) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.calculate_elevation_angle_improved) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    main.o(i.calculate_high_precision_angle) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.calculate_high_precision_angle) refers to fabs.o(i.__hardfp_fabs) for __hardfp_fabs
    main.o(i.calculate_high_precision_angle) refers to cdcmple.o(.text) for __aeabi_cdcmple
    main.o(i.calculate_high_precision_angle) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    main.o(i.calculate_high_precision_angle) refers to dmul.o(.text) for __aeabi_dmul
    main.o(i.calculate_high_precision_angle) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.calculate_high_precision_angle) refers to dadd.o(.text) for __aeabi_dadd
    main.o(i.calculate_light_angle) refers to f2d.o(.text) for __aeabi_f2d
    main.o(i.calculate_light_angle) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    main.o(i.calculate_light_angle) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.calculate_light_angle) refers to dmul.o(.text) for __aeabi_dmul
    main.o(i.calculate_light_angle) refers to ddiv.o(.text) for __aeabi_ddiv
    main.o(i.calculate_light_angle) refers to dadd.o(.text) for __aeabi_dadd
    main.o(i.high_precision_adc_read) refers to ads1115.o(i.ADS1115_Read_average_ADC) for ADS1115_Read_average_ADC
    main.o(i.high_precision_adc_read) refers to systick.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to rs485.o(i.rs485_init) for rs485_init
    main.o(i.main) refers to systick.o(i.systick_config) for systick_config
    main.o(i.main) refers to led.o(i.GD_Led_Config) for GD_Led_Config
    main.o(i.main) refers to myiic.o(i.IIC_Init) for IIC_Init
    main.o(i.main) refers to led.o(i.Led_On) for Led_On
    main.o(i.main) refers to main.o(i.high_precision_adc_read) for high_precision_adc_read
    main.o(i.main) refers to main.o(i.calculate_high_precision_angle) for calculate_high_precision_angle
    main.o(i.main) refers to d2f.o(.text) for __aeabi_d2f
    main.o(i.main) refers to main.o(i.calculate_elevation_angle_calibrated) for calculate_elevation_angle_calibrated
    main.o(i.main) refers to main.o(i.angle_to_hex16) for angle_to_hex16
    main.o(i.main) refers to main.o(i.elevation_to_hex16) for elevation_to_hex16
    main.o(i.main) refers to rs485.o(i.rs485_send_data) for rs485_send_data
    main.o(i.main) refers to systick.o(i.delay_us) for delay_us
    main.o(i.main) refers to main.o(.data) for A0_Voltage
    systick.o(i.delay_ms) refers to systick.o(.data) for count_1ms
    systick.o(i.delay_us) refers to systick.o(.data) for count_1us
    systick.o(i.systick_config) refers to gd32f3x0_misc.o(i.systick_clksource_set) for systick_clksource_set
    systick.o(i.systick_config) refers to f2d.o(.text) for __aeabi_f2d
    systick.o(i.systick_config) refers to uart.o(i.DEBUG) for DEBUG
    systick.o(i.systick_config) refers to system_gd32f3x0.o(.data) for SystemCoreClock
    systick.o(i.systick_config) refers to systick.o(.data) for count_1us
    system_gd32f3x0.o(i.SystemCoreClockUpdate) refers to system_gd32f3x0.o(.data) for SystemCoreClock
    system_gd32f3x0.o(i.SystemInit) refers to system_gd32f3x0.o(i._soft_delay_) for _soft_delay_
    system_gd32f3x0.o(i.SystemInit) refers to system_gd32f3x0.o(i.system_clock_config) for system_clock_config
    system_gd32f3x0.o(i.SystemInit) refers to gd32f3x0_misc.o(i.nvic_vector_table_set) for nvic_vector_table_set
    system_gd32f3x0.o(i.system_clock_config) refers to system_gd32f3x0.o(i.system_clock_72m_hxtal) for system_clock_72m_hxtal
    startup_gd32f3x0.o(RESET) refers to startup_gd32f3x0.o(STACK) for __initial_sp
    startup_gd32f3x0.o(RESET) refers to startup_gd32f3x0.o(.text) for Reset_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f3x0.o(RESET) refers to rs485.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_gd32f3x0.o(.text) refers to system_gd32f3x0.o(i.SystemInit) for SystemInit
    startup_gd32f3x0.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_usart.o(i.usart_deinit) for usart_deinit
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_usart.o(i.usart_receive_config) for usart_receive_config
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_usart.o(i.usart_transmit_config) for usart_transmit_config
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f3x0_usart.o(i.usart_enable) for usart_enable
    gd32f350r_eval.o(i.gd_eval_com_init) refers to gd32f350r_eval.o(.data) for COM_CLK
    gd32f350r_eval.o(i.gd_eval_key_init) refers to gd32f3x0_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f350r_eval.o(i.gd_eval_key_init) refers to gd32f3x0_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f350r_eval.o(i.gd_eval_key_init) refers to gd32f3x0_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f350r_eval.o(i.gd_eval_key_init) refers to gd32f3x0_syscfg.o(i.syscfg_exti_line_config) for syscfg_exti_line_config
    gd32f350r_eval.o(i.gd_eval_key_init) refers to gd32f3x0_exti.o(i.exti_init) for exti_init
    gd32f350r_eval.o(i.gd_eval_key_init) refers to gd32f3x0_exti.o(i.exti_interrupt_flag_clear) for exti_interrupt_flag_clear
    gd32f350r_eval.o(i.gd_eval_key_init) refers to gd32f350r_eval.o(.data) for KEY_CLK
    gd32f350r_eval.o(i.gd_eval_key_state_get) refers to gd32f3x0_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    gd32f350r_eval.o(i.gd_eval_key_state_get) refers to gd32f350r_eval.o(.data) for KEY_PIN
    gd32f350r_eval.o(i.gd_eval_led_init) refers to gd32f3x0_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f350r_eval.o(i.gd_eval_led_init) refers to gd32f3x0_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f350r_eval.o(i.gd_eval_led_init) refers to gd32f3x0_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f350r_eval.o(i.gd_eval_led_init) refers to gd32f350r_eval.o(.data) for GPIO_CLK
    gd32f350r_eval.o(i.gd_eval_led_off) refers to gd32f350r_eval.o(.data) for GPIO_PIN
    gd32f350r_eval.o(i.gd_eval_led_on) refers to gd32f350r_eval.o(.data) for GPIO_PIN
    gd32f350r_eval.o(i.gd_eval_led_toggle) refers to gd32f350r_eval.o(.data) for GPIO_PIN
    gd32f3x0_adc.o(i.adc_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_adc.o(i.adc_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_ctc.o(i.ctc_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_ctc.o(i.ctc_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_dma.o(i.dma_init) refers to gd32f3x0_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd32f3x0_fmc.o(i.fmc_halfword_program) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.fmc_mass_erase) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.fmc_page_erase) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.fmc_ready_wait) refers to gd32f3x0_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f3x0_fmc.o(i.fmc_wait_state_disable) refers to gd32f3x0_fmc.o(i.fmc_unlock) for fmc_unlock
    gd32f3x0_fmc.o(i.fmc_wait_state_disable) refers to gd32f3x0_fmc.o(i.fmc_lock) for fmc_lock
    gd32f3x0_fmc.o(i.fmc_wait_state_enable) refers to gd32f3x0_fmc.o(i.fmc_unlock) for fmc_unlock
    gd32f3x0_fmc.o(i.fmc_wait_state_enable) refers to gd32f3x0_fmc.o(i.fmc_lock) for fmc_lock
    gd32f3x0_fmc.o(i.fmc_word_program) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.fmc_word_reprogram) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.ob_data_program) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.ob_data_program) refers to gd32f3x0_fmc.o(i.ob_parm_get) for ob_parm_get
    gd32f3x0_fmc.o(i.ob_data_program) refers to gd32f3x0_fmc.o(i.ob_value_modify) for ob_value_modify
    gd32f3x0_fmc.o(i.ob_erase) refers to gd32f3x0_fmc.o(i.ob_obstat_plevel_get) for ob_obstat_plevel_get
    gd32f3x0_fmc.o(i.ob_erase) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.ob_security_protection_config) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.ob_security_protection_config) refers to gd32f3x0_fmc.o(i.ob_parm_get) for ob_parm_get
    gd32f3x0_fmc.o(i.ob_security_protection_config) refers to gd32f3x0_fmc.o(i.ob_obstat_plevel_get) for ob_obstat_plevel_get
    gd32f3x0_fmc.o(i.ob_security_protection_config) refers to gd32f3x0_fmc.o(i.ob_value_modify) for ob_value_modify
    gd32f3x0_fmc.o(i.ob_user_write) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.ob_user_write) refers to gd32f3x0_fmc.o(i.ob_parm_get) for ob_parm_get
    gd32f3x0_fmc.o(i.ob_user_write) refers to gd32f3x0_fmc.o(i.ob_value_modify) for ob_value_modify
    gd32f3x0_fmc.o(i.ob_write_protection_enable) refers to gd32f3x0_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(i.ob_write_protection_enable) refers to gd32f3x0_fmc.o(i.ob_parm_get) for ob_parm_get
    gd32f3x0_fmc.o(i.ob_write_protection_enable) refers to gd32f3x0_fmc.o(i.ob_value_modify) for ob_value_modify
    gd32f3x0_gpio.o(i.gpio_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_gpio.o(i.gpio_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_i2c.o(i.i2c_clock_config) refers to gd32f3x0_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f3x0_i2c.o(i.i2c_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_i2c.o(i.i2c_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_misc.o(i.nvic_irq_enable) refers to gd32f3x0_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f3x0_pmu.o(i.pmu_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_pmu.o(i.pmu_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f3x0_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f3x0_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f3x0_pmu.o(.bss) for reg_snap
    gd32f3x0_rcu.o(i.rcu_osci_stab_wait) refers to gd32f3x0_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f3x0_rtc.o(i.rtc_deinit) refers to gd32f3x0_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f3x0_rtc.o(i.rtc_deinit) refers to gd32f3x0_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f3x0_rtc.o(i.rtc_init) refers to gd32f3x0_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f3x0_rtc.o(i.rtc_init) refers to gd32f3x0_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f3x0_rtc.o(i.rtc_init) refers to gd32f3x0_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f3x0_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f3x0_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f3x0_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f3x0_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f3x0_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f3x0_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f3x0_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f3x0_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f3x0_rtc.o(i.rtc_second_adjust) refers to gd32f3x0_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f3x0_spi.o(i.i2s_psc_config) refers to gd32f3x0_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f3x0_spi.o(i.spi_i2s_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_spi.o(i.spi_i2s_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_syscfg.o(i.syscfg_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_syscfg.o(i.syscfg_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_timer.o(i.timer_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_timer.o(i.timer_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_timer.o(i.timer_external_clock_mode0_config) refers to gd32f3x0_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f3x0_timer.o(i.timer_external_clock_mode1_config) refers to gd32f3x0_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f3x0_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f3x0_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f3x0_timer.o(i.timer_input_capture_config) refers to gd32f3x0_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f3x0_timer.o(i.timer_input_pwm_capture_config) refers to gd32f3x0_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f3x0_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f3x0_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f3x0_usart.o(i.usart_baudrate_set) refers to gd32f3x0_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f3x0_usart.o(i.usart_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_usart.o(i.usart_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_wwdgt.o(i.wwdgt_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_wwdgt.o(i.wwdgt_deinit) refers to gd32f3x0_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    uart.o(i.DEBUG) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart.o(i.DEBUG) refers to strlen.o(.text) for strlen
    uart.o(i.DEBUG) refers to uart.o(i.uart_send_data) for uart_send_data
    uart.o(i.GD_USART_Config) refers to gd32f3x0_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    uart.o(i.GD_USART_Config) refers to gd32f3x0_gpio.o(i.gpio_af_set) for gpio_af_set
    uart.o(i.GD_USART_Config) refers to gd32f3x0_gpio.o(i.gpio_mode_set) for gpio_mode_set
    uart.o(i.GD_USART_Config) refers to gd32f3x0_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_deinit) for usart_deinit
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_word_length_set) for usart_word_length_set
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_parity_config) for usart_parity_config
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_receive_config) for usart_receive_config
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_transmit_config) for usart_transmit_config
    uart.o(i.GD_USART_Config) refers to gd32f3x0_usart.o(i.usart_enable) for usart_enable
    uart.o(i.fputc) refers to gd32f3x0_usart.o(i.usart_data_transmit) for usart_data_transmit
    uart.o(i.fputc) refers to gd32f3x0_usart.o(i.usart_flag_get) for usart_flag_get
    uart.o(i.uart_send_data) refers to gd32f3x0_usart.o(i.usart_flag_get) for usart_flag_get
    uart.o(i.uart_send_data) refers to gd32f3x0_usart.o(i.usart_data_transmit) for usart_data_transmit
    led.o(i.GD_Led_Config) refers to gd32f3x0_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    led.o(i.GD_Led_Config) refers to gd32f3x0_gpio.o(i.gpio_mode_set) for gpio_mode_set
    led.o(i.GD_Led_Config) refers to gd32f3x0_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    led.o(i.GD_Led_Config) refers to gd32f3x0_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    led.o(i.Led_Fz) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    led.o(i.Led_Fz) refers to systick.o(i.delay_ms) for delay_ms
    led.o(i.Led_Off) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    led.o(i.Led_On) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    led.o(i.Led_Toggle) refers to gd32f3x0_gpio.o(i.gpio_bit_toggle) for gpio_bit_toggle
    myiic.o(i.IIC_Ack) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.IIC_Ack) refers to myiic.o(i.IIC_delay) for IIC_delay
    myiic.o(i.IIC_Init) refers to gd32f3x0_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    myiic.o(i.IIC_Init) refers to gd32f3x0_gpio.o(i.gpio_mode_set) for gpio_mode_set
    myiic.o(i.IIC_Init) refers to gd32f3x0_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    myiic.o(i.IIC_Init) refers to myiic.o(i.IIC_Stop) for IIC_Stop
    myiic.o(i.IIC_NAck) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.IIC_NAck) refers to myiic.o(i.IIC_delay) for IIC_delay
    myiic.o(i.IIC_Read_Byte) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.IIC_Read_Byte) refers to myiic.o(i.IIC_delay) for IIC_delay
    myiic.o(i.IIC_Read_Byte) refers to gd32f3x0_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    myiic.o(i.IIC_Read_Byte) refers to myiic.o(i.IIC_NAck) for IIC_NAck
    myiic.o(i.IIC_Read_Byte) refers to myiic.o(i.IIC_Ack) for IIC_Ack
    myiic.o(i.IIC_SendByte) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.IIC_SendByte) refers to myiic.o(i.IIC_delay) for IIC_delay
    myiic.o(i.IIC_Start) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.IIC_Start) refers to myiic.o(i.IIC_delay) for IIC_delay
    myiic.o(i.IIC_Stop) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.IIC_Stop) refers to myiic.o(i.IIC_delay) for IIC_delay
    myiic.o(i.IIC_Wait_Ack) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    myiic.o(i.IIC_Wait_Ack) refers to myiic.o(i.IIC_delay) for IIC_delay
    myiic.o(i.IIC_delay) refers to systick.o(i.delay_us) for delay_us
    ads1115.o(i.ADS1115_Read_ADC) refers to ads1115.o(i.ADS1115_Write) for ADS1115_Write
    ads1115.o(i.ADS1115_Read_ADC) refers to systick.o(i.delay_us) for delay_us
    ads1115.o(i.ADS1115_Read_ADC) refers to ads1115.o(i.ADS1115_Read_adc) for ADS1115_Read_adc
    ads1115.o(i.ADS1115_Read_ADC) refers to dflti.o(.text) for __aeabi_i2d
    ads1115.o(i.ADS1115_Read_ADC) refers to dmul.o(.text) for __aeabi_dmul
    ads1115.o(i.ADS1115_Read_ADC) refers to d2f.o(.text) for __aeabi_d2f
    ads1115.o(i.ADS1115_Read_adc) refers to myiic.o(i.IIC_Start) for IIC_Start
    ads1115.o(i.ADS1115_Read_adc) refers to myiic.o(i.IIC_SendByte) for IIC_SendByte
    ads1115.o(i.ADS1115_Read_adc) refers to ads1115.o(i.IIC_ReceiveAck) for IIC_ReceiveAck
    ads1115.o(i.ADS1115_Read_adc) refers to myiic.o(i.IIC_Read_Byte) for IIC_Read_Byte
    ads1115.o(i.ADS1115_Read_adc) refers to myiic.o(i.IIC_Stop) for IIC_Stop
    ads1115.o(i.ADS1115_Read_average_ADC) refers to ads1115.o(i.ADS1115_Read_ADC) for ADS1115_Read_ADC
    ads1115.o(i.ADS1115_Read_average_ADC) refers to ads1115.o(i.ADS1115_Read_adc) for ADS1115_Read_adc
    ads1115.o(i.ADS1115_Write) refers to myiic.o(i.IIC_Start) for IIC_Start
    ads1115.o(i.ADS1115_Write) refers to myiic.o(i.IIC_SendByte) for IIC_SendByte
    ads1115.o(i.ADS1115_Write) refers to myiic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    ads1115.o(i.ADS1115_Write) refers to myiic.o(i.IIC_Stop) for IIC_Stop
    ads1115.o(i.IIC_ReceiveAck) refers to gd32f3x0_gpio.o(i.gpio_bit_write) for gpio_bit_write
    ads1115.o(i.IIC_ReceiveAck) refers to systick.o(i.delay_us) for delay_us
    crc16.o(i.Modbus_CRC16) refers to crc16.o(.constdata) for auchCRCHi
    rs485.o(i.USART1_IRQHandler) refers to gd32f3x0_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    rs485.o(i.USART1_IRQHandler) refers to gd32f3x0_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    rs485.o(i.USART1_IRQHandler) refers to gd32f3x0_usart.o(i.usart_data_receive) for usart_data_receive
    rs485.o(i.USART1_IRQHandler) refers to rs485.o(.data) for rs485_rx_index
    rs485.o(i.USART1_IRQHandler) refers to rs485.o(.bss) for rs485_rx_buffer
    rs485.o(i.modbus_handle) refers to crc16.o(i.Modbus_CRC16) for Modbus_CRC16
    rs485.o(i.modbus_handle) refers to rs485.o(i.rs485_printf) for rs485_printf
    rs485.o(i.modbus_handle) refers to rs485.o(i.rs485_send_data) for rs485_send_data
    rs485.o(i.modbus_handle) refers to memseta.o(.text) for __aeabi_memclr4
    rs485.o(i.modbus_handle) refers to systick.o(i.delay_ms) for delay_ms
    rs485.o(i.modbus_handle) refers to rs485.o(.bss) for Adc_Sensor
    rs485.o(i.rs485_init) refers to gd32f3x0_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rs485.o(i.rs485_init) refers to gd32f3x0_gpio.o(i.gpio_mode_set) for gpio_mode_set
    rs485.o(i.rs485_init) refers to gd32f3x0_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    rs485.o(i.rs485_init) refers to gd32f3x0_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    rs485.o(i.rs485_init) refers to gd32f3x0_gpio.o(i.gpio_af_set) for gpio_af_set
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_deinit) for usart_deinit
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_word_length_set) for usart_word_length_set
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_parity_config) for usart_parity_config
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_receive_config) for usart_receive_config
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_transmit_config) for usart_transmit_config
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    rs485.o(i.rs485_init) refers to gd32f3x0_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    rs485.o(i.rs485_init) refers to gd32f3x0_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    rs485.o(i.rs485_init) refers to gd32f3x0_usart.o(i.usart_enable) for usart_enable
    rs485.o(i.rs485_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    rs485.o(i.rs485_printf) refers to strlen.o(.text) for strlen
    rs485.o(i.rs485_printf) refers to rs485.o(i.rs485_send_data) for rs485_send_data
    rs485.o(i.rs485_receive_data) refers to systick.o(i.delay_ms) for delay_ms
    rs485.o(i.rs485_receive_data) refers to rs485.o(.data) for rs485_rx_index
    rs485.o(i.rs485_receive_data) refers to rs485.o(.bss) for rs485_rx_buffer
    rs485.o(i.rs485_rx_clear) refers to memseta.o(.text) for __aeabi_memclr4
    rs485.o(i.rs485_rx_clear) refers to rs485.o(.bss) for rs485_rx_buffer
    rs485.o(i.rs485_rx_clear) refers to rs485.o(.data) for rs485_rx_index
    rs485.o(i.rs485_send_data) refers to rs485.o(i.rs485_set_tx_mode) for rs485_set_tx_mode
    rs485.o(i.rs485_send_data) refers to gd32f3x0_usart.o(i.usart_flag_get) for usart_flag_get
    rs485.o(i.rs485_send_data) refers to gd32f3x0_usart.o(i.usart_data_transmit) for usart_data_transmit
    rs485.o(i.rs485_send_data) refers to rs485.o(i.rs485_set_rx_mode) for rs485_set_rx_mode
    rs485.o(i.rs485_set_rx_mode) refers to gd32f3x0_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    rs485.o(i.rs485_set_tx_mode) refers to gd32f3x0_gpio.o(i.gpio_bit_set) for gpio_bit_set
    rs485.o(i.sensor_struct_init) refers to rs485.o(.bss) for Adc_Sensor
    acos.o(i.__hardfp_acos) refers (Special) to iusefp.o(.text) for __I$use$fp
    acos.o(i.__hardfp_acos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    acos.o(i.__hardfp_acos) refers to errno.o(i.__set_errno) for __set_errno
    acos.o(i.__hardfp_acos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    acos.o(i.__hardfp_acos) refers to dmul.o(.text) for __aeabi_dmul
    acos.o(i.__hardfp_acos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    acos.o(i.__hardfp_acos) refers to dadd.o(.text) for __aeabi_dadd
    acos.o(i.__hardfp_acos) refers to ddiv.o(.text) for __aeabi_ddiv
    acos.o(i.__hardfp_acos) refers to sqrt.o(i.sqrt) for sqrt
    acos.o(i.__hardfp_acos) refers to acos.o(.constdata) for .constdata
    acos.o(i.__softfp_acos) refers (Special) to iusefp.o(.text) for __I$use$fp
    acos.o(i.__softfp_acos) refers to acos.o(i.__hardfp_acos) for __hardfp_acos
    acos.o(i.acos) refers (Special) to iusefp.o(.text) for __I$use$fp
    acos.o(i.acos) refers to acos.o(i.__hardfp_acos) for __hardfp_acos
    acos.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    acos_x.o(i.____hardfp_acos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    acos_x.o(i.____hardfp_acos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    acos_x.o(i.____hardfp_acos$lsc) refers to errno.o(i.__set_errno) for __set_errno
    acos_x.o(i.____hardfp_acos$lsc) refers to dmul.o(.text) for __aeabi_dmul
    acos_x.o(i.____hardfp_acos$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    acos_x.o(i.____hardfp_acos$lsc) refers to dadd.o(.text) for __aeabi_dadd
    acos_x.o(i.____hardfp_acos$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    acos_x.o(i.____hardfp_acos$lsc) refers to sqrt.o(i.sqrt) for sqrt
    acos_x.o(i.____hardfp_acos$lsc) refers to acos_x.o(.constdata) for .constdata
    acos_x.o(i.____softfp_acos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    acos_x.o(i.____softfp_acos$lsc) refers to acos_x.o(i.____hardfp_acos$lsc) for ____hardfp_acos$lsc
    acos_x.o(i.__acos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    acos_x.o(i.__acos$lsc) refers to acos_x.o(i.____hardfp_acos$lsc) for ____hardfp_acos$lsc
    acos_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to errno.o(i.__set_errno) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflti.o(.text) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(.text) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to dadd.o(.text) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to uart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to uart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to uart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to uart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f3x0.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f3x0.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(i.calculate_elevation_angle_by_asymmetry), (140 bytes).
    Removing main.o(i.calculate_elevation_angle_by_intensity), (212 bytes).
    Removing main.o(i.calculate_elevation_angle_improved), (440 bytes).
    Removing main.o(i.calculate_light_angle), (228 bytes).
    Removing gd32f3x0_it.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_it.o(.revsh_text), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing system_gd32f3x0.o(.rev16_text), (4 bytes).
    Removing system_gd32f3x0.o(.revsh_text), (4 bytes).
    Removing system_gd32f3x0.o(i.SystemCoreClockUpdate), (340 bytes).
    Removing system_gd32f3x0.o(i.gd32f3x0_firmware_version_get), (6 bytes).
    Removing startup_gd32f3x0.o(HEAP), (1024 bytes).
    Removing gd32f350r_eval.o(.rev16_text), (4 bytes).
    Removing gd32f350r_eval.o(.revsh_text), (4 bytes).
    Removing gd32f350r_eval.o(i.gd_eval_com_init), (192 bytes).
    Removing gd32f350r_eval.o(i.gd_eval_key_init), (128 bytes).
    Removing gd32f350r_eval.o(i.gd_eval_key_state_get), (32 bytes).
    Removing gd32f350r_eval.o(i.gd_eval_led_init), (84 bytes).
    Removing gd32f350r_eval.o(i.gd_eval_led_off), (24 bytes).
    Removing gd32f350r_eval.o(i.gd_eval_led_on), (24 bytes).
    Removing gd32f350r_eval.o(i.gd_eval_led_toggle), (24 bytes).
    Removing gd32f350r_eval.o(.data), (105 bytes).
    Removing gd32f3x0_adc.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_adc.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_adc.o(i.adc_calibration_enable), (60 bytes).
    Removing gd32f3x0_adc.o(i.adc_channel_length_config), (96 bytes).
    Removing gd32f3x0_adc.o(i.adc_data_alignment_config), (36 bytes).
    Removing gd32f3x0_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_disable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_discontinuous_mode_config), (100 bytes).
    Removing gd32f3x0_adc.o(i.adc_dma_mode_disable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_dma_mode_enable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_enable), (28 bytes).
    Removing gd32f3x0_adc.o(i.adc_external_trigger_config), (84 bytes).
    Removing gd32f3x0_adc.o(i.adc_external_trigger_source_config), (64 bytes).
    Removing gd32f3x0_adc.o(i.adc_flag_clear), (16 bytes).
    Removing gd32f3x0_adc.o(i.adc_flag_get), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_inserted_channel_config), (140 bytes).
    Removing gd32f3x0_adc.o(i.adc_inserted_channel_offset_config), (48 bytes).
    Removing gd32f3x0_adc.o(i.adc_inserted_data_read), (64 bytes).
    Removing gd32f3x0_adc.o(i.adc_interrupt_disable), (60 bytes).
    Removing gd32f3x0_adc.o(i.adc_interrupt_enable), (60 bytes).
    Removing gd32f3x0_adc.o(i.adc_interrupt_flag_clear), (16 bytes).
    Removing gd32f3x0_adc.o(i.adc_interrupt_flag_get), (108 bytes).
    Removing gd32f3x0_adc.o(i.adc_oversample_mode_config), (64 bytes).
    Removing gd32f3x0_adc.o(i.adc_oversample_mode_disable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_oversample_mode_enable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_regular_channel_config), (212 bytes).
    Removing gd32f3x0_adc.o(i.adc_regular_data_read), (12 bytes).
    Removing gd32f3x0_adc.o(i.adc_resolution_config), (28 bytes).
    Removing gd32f3x0_adc.o(i.adc_software_trigger_enable), (44 bytes).
    Removing gd32f3x0_adc.o(i.adc_special_function_config), (128 bytes).
    Removing gd32f3x0_adc.o(i.adc_tempsensor_vrefint_disable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_tempsensor_vrefint_enable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_vbat_disable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_vbat_enable), (20 bytes).
    Removing gd32f3x0_adc.o(i.adc_watchdog_disable), (24 bytes).
    Removing gd32f3x0_adc.o(i.adc_watchdog_group_channel_enable), (84 bytes).
    Removing gd32f3x0_adc.o(i.adc_watchdog_single_channel_enable), (48 bytes).
    Removing gd32f3x0_adc.o(i.adc_watchdog_threshold_config), (24 bytes).
    Removing gd32f3x0_cmp.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_cmp.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_deinit), (36 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_disable), (40 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_enable), (40 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_lock_enable), (40 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_mode_init), (68 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_output_init), (84 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_output_level_get), (44 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_switch_disable), (20 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_switch_enable), (20 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_window_disable), (20 bytes).
    Removing gd32f3x0_cmp.o(i.cmp_window_enable), (20 bytes).
    Removing gd32f3x0_crc.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_crc.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_crc.o(i.crc_block_data_calculate), (96 bytes).
    Removing gd32f3x0_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f3x0_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f3x0_crc.o(i.crc_deinit), (32 bytes).
    Removing gd32f3x0_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f3x0_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f3x0_crc.o(i.crc_init_data_register_write), (12 bytes).
    Removing gd32f3x0_crc.o(i.crc_input_data_reverse_config), (28 bytes).
    Removing gd32f3x0_crc.o(i.crc_polynomial_set), (20 bytes).
    Removing gd32f3x0_crc.o(i.crc_polynomial_size_set), (28 bytes).
    Removing gd32f3x0_crc.o(i.crc_reverse_output_data_disable), (20 bytes).
    Removing gd32f3x0_crc.o(i.crc_reverse_output_data_enable), (28 bytes).
    Removing gd32f3x0_crc.o(i.crc_single_data_calculate), (36 bytes).
    Removing gd32f3x0_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_clock_limit_value_config), (36 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_counter_direction_read), (20 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_flag_get), (20 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_irc48m_trim_value_config), (36 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f3x0_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f3x0_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f3x0_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f3x0_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f3x0_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f3x0_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f3x0_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f3x0_dma.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_dma.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_dma.o(i.dma_channel_disable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_channel_enable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_circulation_disable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_circulation_enable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_deinit), (92 bytes).
    Removing gd32f3x0_dma.o(i.dma_flag_clear), (24 bytes).
    Removing gd32f3x0_dma.o(i.dma_flag_get), (32 bytes).
    Removing gd32f3x0_dma.o(i.dma_init), (284 bytes).
    Removing gd32f3x0_dma.o(i.dma_interrupt_disable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_interrupt_enable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_interrupt_flag_clear), (24 bytes).
    Removing gd32f3x0_dma.o(i.dma_interrupt_flag_get), (140 bytes).
    Removing gd32f3x0_dma.o(i.dma_memory_address_config), (20 bytes).
    Removing gd32f3x0_dma.o(i.dma_memory_increase_disable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_memory_increase_enable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_memory_to_memory_disable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_memory_to_memory_enable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_memory_width_config), (40 bytes).
    Removing gd32f3x0_dma.o(i.dma_periph_address_config), (20 bytes).
    Removing gd32f3x0_dma.o(i.dma_periph_increase_disable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_periph_increase_enable), (36 bytes).
    Removing gd32f3x0_dma.o(i.dma_periph_width_config), (40 bytes).
    Removing gd32f3x0_dma.o(i.dma_priority_config), (40 bytes).
    Removing gd32f3x0_dma.o(i.dma_struct_para_init), (22 bytes).
    Removing gd32f3x0_dma.o(i.dma_transfer_direction_config), (68 bytes).
    Removing gd32f3x0_dma.o(i.dma_transfer_number_config), (24 bytes).
    Removing gd32f3x0_dma.o(i.dma_transfer_number_get), (20 bytes).
    Removing gd32f3x0_exti.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_exti.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_exti.o(i.exti_deinit), (32 bytes).
    Removing gd32f3x0_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f3x0_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f3x0_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f3x0_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f3x0_exti.o(i.exti_init), (188 bytes).
    Removing gd32f3x0_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f3x0_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f3x0_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f3x0_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f3x0_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f3x0_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f3x0_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_flag_get), (20 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_halfword_program), (64 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_interrupt_flag_get), (20 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_mass_erase), (68 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_page_erase), (76 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_ready_wait), (34 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_state_get), (48 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_wait_state_disable), (32 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_wait_state_enable), (32 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_word_program), (64 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_word_reprogram), (96 bytes).
    Removing gd32f3x0_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f3x0_fmc.o(i.ob_data_get), (12 bytes).
    Removing gd32f3x0_fmc.o(i.ob_data_program), (200 bytes).
    Removing gd32f3x0_fmc.o(i.ob_erase), (164 bytes).
    Removing gd32f3x0_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f3x0_fmc.o(i.ob_obstat_plevel_get), (16 bytes).
    Removing gd32f3x0_fmc.o(i.ob_parm_get), (52 bytes).
    Removing gd32f3x0_fmc.o(i.ob_reset), (20 bytes).
    Removing gd32f3x0_fmc.o(i.ob_security_protection_config), (156 bytes).
    Removing gd32f3x0_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f3x0_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f3x0_fmc.o(i.ob_user_write), (144 bytes).
    Removing gd32f3x0_fmc.o(i.ob_value_modify), (172 bytes).
    Removing gd32f3x0_fmc.o(i.ob_write_protection_enable), (248 bytes).
    Removing gd32f3x0_fmc.o(i.ob_write_protection_get), (12 bytes).
    Removing gd32f3x0_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_window_value_config), (64 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f3x0_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f3x0_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f3x0_gpio.o(i.gpio_deinit), (148 bytes).
    Removing gd32f3x0_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f3x0_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f3x0_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f3x0_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f3x0_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f3x0_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f3x0_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_clock_config), (312 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_deinit), (64 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f3x0_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f3x0_misc.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_misc.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f3x0_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f3x0_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f3x0_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_backup_write_enable), (20 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_flag_clear), (44 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_flag_get), (20 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_lowdriver_mode_disable), (24 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_lowdriver_mode_enable), (28 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_to_standbymode), (96 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_wakeup_pin_disable), (16 bytes).
    Removing gd32f3x0_pmu.o(i.pmu_wakeup_pin_enable), (16 bytes).
    Removing gd32f3x0_pmu.o(.bss), (16 bytes).
    Removing gd32f3x0_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_adc_clock_config), (332 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_ahb_clock_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_apb1_clock_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_apb2_clock_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_cec_clock_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_ck48m_clock_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_ckout_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_deepsleep_voltage_set), (36 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_deinit), (176 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_flag_get), (36 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_hxtal_prediv_config), (24 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_interrupt_disable), (32 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_interrupt_enable), (32 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_interrupt_flag_clear), (32 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_irc28m_adjust_value_set), (24 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_irc8m_adjust_value_set), (24 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_lxtal_drive_capability_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_osci_bypass_mode_disable), (108 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_osci_bypass_mode_enable), (108 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_osci_off), (32 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_osci_on), (32 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_osci_stab_wait), (316 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_periph_clock_disable), (32 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_periph_clock_sleep_disable), (32 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_periph_clock_sleep_enable), (32 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_pll_config), (64 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_pll_preselection_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_rtc_clock_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_system_clock_source_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_usart_clock_config), (28 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_usbfs_clock_config), (56 bytes).
    Removing gd32f3x0_rcu.o(i.rcu_voltage_key_unlock), (40 bytes).
    Removing gd32f3x0_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_alarm_config), (84 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_alarm_disable), (80 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_alarm_enable), (40 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_alarm_get), (56 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_alarm_subsecond_config), (32 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_alarm_subsecond_get), (16 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_alter_output_config), (76 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_calibration_config), (76 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_current_time_get), (100 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_deinit), (128 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_init), (196 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_init_mode_enter), (68 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_init_mode_exit), (20 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_register_sync_wait), (96 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_tamper_enable), (164 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f3x0_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f3x0_spi.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_spi.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f3x0_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f3x0_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f3x0_spi.o(i.i2s_psc_config), (164 bytes).
    Removing gd32f3x0_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f3x0_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f3x0_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f3x0_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f3x0_spi.o(i.spi_crc_polynomial_set), (12 bytes).
    Removing gd32f3x0_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f3x0_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f3x0_spi.o(i.spi_enable), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_data_receive), (8 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_data_transmit), (4 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_deinit), (64 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_flag_get), (16 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_interrupt_disable), (8 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_interrupt_enable), (8 bytes).
    Removing gd32f3x0_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f3x0_spi.o(i.spi_init), (50 bytes).
    Removing gd32f3x0_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_nssp_mode_disable), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_nssp_mode_enable), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f3x0_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f3x0_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f3x0_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f3x0_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f3x0_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f3x0_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f3x0_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f3x0_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f3x0_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_cps_rdy_flag_get), (24 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_dma_remap_disable), (16 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_dma_remap_enable), (16 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_exti_line_config), (160 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_flag_clear), (16 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_high_current_disable), (20 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_high_current_enable), (20 bytes).
    Removing gd32f3x0_syscfg.o(i.syscfg_lock_config), (16 bytes).
    Removing gd32f3x0_timer.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_timer.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f3x0_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f3x0_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_complementary_output_polarity_config), (92 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_config), (520 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f3x0_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f3x0_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f3x0_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f3x0_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f3x0_timer.o(i.timer_deinit), (172 bytes).
    Removing gd32f3x0_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f3x0_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f3x0_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f3x0_timer.o(i.timer_enable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f3x0_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f3x0_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f3x0_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f3x0_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f3x0_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f3x0_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f3x0_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f3x0_timer.o(i.timer_init), (160 bytes).
    Removing gd32f3x0_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f3x0_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f3x0_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f3x0_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f3x0_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f3x0_timer.o(i.timer_interrupt_enable), (8 bytes).
    Removing gd32f3x0_timer.o(i.timer_interrupt_flag_clear), (6 bytes).
    Removing gd32f3x0_timer.o(i.timer_interrupt_flag_get), (24 bytes).
    Removing gd32f3x0_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f3x0_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f3x0_timer.o(i.timer_ocpre_clear_source_config), (26 bytes).
    Removing gd32f3x0_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f3x0_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f3x0_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f3x0_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f3x0_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f3x0_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f3x0_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f3x0_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f3x0_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f3x0_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f3x0_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f3x0_usart.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_usart.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_usart.o(i.usart_address_config), (32 bytes).
    Removing gd32f3x0_usart.o(i.usart_address_detection_mode_config), (28 bytes).
    Removing gd32f3x0_usart.o(i.usart_block_length_config), (24 bytes).
    Removing gd32f3x0_usart.o(i.usart_clock_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_clock_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_command_enable), (8 bytes).
    Removing gd32f3x0_usart.o(i.usart_data_first_config), (24 bytes).
    Removing gd32f3x0_usart.o(i.usart_depolarity_config), (28 bytes).
    Removing gd32f3x0_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f3x0_usart.o(i.usart_dma_receive_config), (20 bytes).
    Removing gd32f3x0_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f3x0_usart.o(i.usart_driver_assertime_config), (32 bytes).
    Removing gd32f3x0_usart.o(i.usart_driver_deassertime_config), (32 bytes).
    Removing gd32f3x0_usart.o(i.usart_flag_clear), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_guard_time_config), (32 bytes).
    Removing gd32f3x0_usart.o(i.usart_halfduplex_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_halfduplex_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f3x0_usart.o(i.usart_invert_config), (110 bytes).
    Removing gd32f3x0_usart.o(i.usart_irda_lowpower_config), (28 bytes).
    Removing gd32f3x0_usart.o(i.usart_irda_mode_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_irda_mode_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_lin_break_detection_length_config), (28 bytes).
    Removing gd32f3x0_usart.o(i.usart_lin_mode_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_lin_mode_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f3x0_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f3x0_usart.o(i.usart_mute_mode_wakeup_config), (24 bytes).
    Removing gd32f3x0_usart.o(i.usart_overrun_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_overrun_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_oversample_config), (24 bytes).
    Removing gd32f3x0_usart.o(i.usart_prescaler_config), (24 bytes).
    Removing gd32f3x0_usart.o(i.usart_receive_fifo_counter_number), (12 bytes).
    Removing gd32f3x0_usart.o(i.usart_receive_fifo_disable), (14 bytes).
    Removing gd32f3x0_usart.o(i.usart_receive_fifo_enable), (14 bytes).
    Removing gd32f3x0_usart.o(i.usart_receiver_timeout_disable), (10 bytes).
    Removing gd32f3x0_usart.o(i.usart_receiver_timeout_enable), (10 bytes).
    Removing gd32f3x0_usart.o(i.usart_receiver_timeout_threshold_config), (16 bytes).
    Removing gd32f3x0_usart.o(i.usart_reception_error_dma_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_reception_error_dma_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_rs485_driver_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_rs485_driver_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_sample_bit_config), (24 bytes).
    Removing gd32f3x0_usart.o(i.usart_smartcard_autoretry_config), (32 bytes).
    Removing gd32f3x0_usart.o(i.usart_smartcard_mode_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_smartcard_mode_early_nack_disable), (14 bytes).
    Removing gd32f3x0_usart.o(i.usart_smartcard_mode_early_nack_enable), (14 bytes).
    Removing gd32f3x0_usart.o(i.usart_smartcard_mode_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_smartcard_mode_nack_disable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_smartcard_mode_nack_enable), (18 bytes).
    Removing gd32f3x0_usart.o(i.usart_synchronous_clock_config), (50 bytes).
    Removing gd32f3x0_usart.o(i.usart_wakeup_disable), (10 bytes).
    Removing gd32f3x0_usart.o(i.usart_wakeup_enable), (10 bytes).
    Removing gd32f3x0_usart.o(i.usart_wakeup_mode_config), (28 bytes).
    Removing gd32f3x0_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f3x0_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f3x0_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f3x0_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f3x0_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f3x0_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f3x0_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f3x0_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f3x0_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing uart.o(i.GD_USART_Config), (156 bytes).
    Removing uart.o(i.fputc), (40 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(i.Led_Fz), (44 bytes).
    Removing led.o(i.Led_Off), (20 bytes).
    Removing led.o(i.Led_Toggle), (16 bytes).
    Removing myiic.o(.rev16_text), (4 bytes).
    Removing myiic.o(.revsh_text), (4 bytes).
    Removing ads1115.o(.rev16_text), (4 bytes).
    Removing ads1115.o(.revsh_text), (4 bytes).
    Removing crc16.o(.rev16_text), (4 bytes).
    Removing crc16.o(.revsh_text), (4 bytes).
    Removing crc16.o(i.Modbus_CRC16), (56 bytes).
    Removing crc16.o(.constdata), (512 bytes).
    Removing rs485.o(.rev16_text), (4 bytes).
    Removing rs485.o(.revsh_text), (4 bytes).
    Removing rs485.o(i.modbus_handle), (508 bytes).
    Removing rs485.o(i.rs485_printf), (50 bytes).
    Removing rs485.o(i.rs485_receive_data), (64 bytes).
    Removing rs485.o(i.rs485_rx_clear), (28 bytes).
    Removing rs485.o(i.sensor_struct_init), (44 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dsqrt.o(.text), (162 bytes).

578 unused section(s) (total 24475 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/acos.c                        0x00000000   Number         0  acos.o ABSOLUTE
    ../mathlib/acos.c                        0x00000000   Number         0  acos_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s 0x00000000   Number         0  startup_gd32f3x0.o ABSOLUTE
    ..\CMSIS\GD\GD32F3x0\Source\system_gd32f3x0.c 0x00000000   Number         0  system_gd32f3x0.o ABSOLUTE
    ..\HARDWARE\IIC\myiic.c                  0x00000000   Number         0  myiic.o ABSOLUTE
    ..\HARDWARE\RS485.c                      0x00000000   Number         0  rs485.o ABSOLUTE
    ..\HARDWARE\UART.c                       0x00000000   Number         0  uart.o ABSOLUTE
    ..\HARDWARE\ads1115.c                    0x00000000   Number         0  ads1115.o ABSOLUTE
    ..\HARDWARE\crc16.c                      0x00000000   Number         0  crc16.o ABSOLUTE
    ..\HARDWARE\led.c                        0x00000000   Number         0  led.o ABSOLUTE
    ..\Library\Source\gd32f3x0_adc.c         0x00000000   Number         0  gd32f3x0_adc.o ABSOLUTE
    ..\Library\Source\gd32f3x0_cec.c         0x00000000   Number         0  gd32f3x0_cec.o ABSOLUTE
    ..\Library\Source\gd32f3x0_cmp.c         0x00000000   Number         0  gd32f3x0_cmp.o ABSOLUTE
    ..\Library\Source\gd32f3x0_crc.c         0x00000000   Number         0  gd32f3x0_crc.o ABSOLUTE
    ..\Library\Source\gd32f3x0_ctc.c         0x00000000   Number         0  gd32f3x0_ctc.o ABSOLUTE
    ..\Library\Source\gd32f3x0_dac.c         0x00000000   Number         0  gd32f3x0_dac.o ABSOLUTE
    ..\Library\Source\gd32f3x0_dbg.c         0x00000000   Number         0  gd32f3x0_dbg.o ABSOLUTE
    ..\Library\Source\gd32f3x0_dma.c         0x00000000   Number         0  gd32f3x0_dma.o ABSOLUTE
    ..\Library\Source\gd32f3x0_exti.c        0x00000000   Number         0  gd32f3x0_exti.o ABSOLUTE
    ..\Library\Source\gd32f3x0_fmc.c         0x00000000   Number         0  gd32f3x0_fmc.o ABSOLUTE
    ..\Library\Source\gd32f3x0_fwdgt.c       0x00000000   Number         0  gd32f3x0_fwdgt.o ABSOLUTE
    ..\Library\Source\gd32f3x0_gpio.c        0x00000000   Number         0  gd32f3x0_gpio.o ABSOLUTE
    ..\Library\Source\gd32f3x0_i2c.c         0x00000000   Number         0  gd32f3x0_i2c.o ABSOLUTE
    ..\Library\Source\gd32f3x0_misc.c        0x00000000   Number         0  gd32f3x0_misc.o ABSOLUTE
    ..\Library\Source\gd32f3x0_pmu.c         0x00000000   Number         0  gd32f3x0_pmu.o ABSOLUTE
    ..\Library\Source\gd32f3x0_rcu.c         0x00000000   Number         0  gd32f3x0_rcu.o ABSOLUTE
    ..\Library\Source\gd32f3x0_rtc.c         0x00000000   Number         0  gd32f3x0_rtc.o ABSOLUTE
    ..\Library\Source\gd32f3x0_spi.c         0x00000000   Number         0  gd32f3x0_spi.o ABSOLUTE
    ..\Library\Source\gd32f3x0_syscfg.c      0x00000000   Number         0  gd32f3x0_syscfg.o ABSOLUTE
    ..\Library\Source\gd32f3x0_timer.c       0x00000000   Number         0  gd32f3x0_timer.o ABSOLUTE
    ..\Library\Source\gd32f3x0_tsi.c         0x00000000   Number         0  gd32f3x0_tsi.o ABSOLUTE
    ..\Library\Source\gd32f3x0_usart.c       0x00000000   Number         0  gd32f3x0_usart.o ABSOLUTE
    ..\Library\Source\gd32f3x0_wwdgt.c       0x00000000   Number         0  gd32f3x0_wwdgt.o ABSOLUTE
    ..\User\gd32f3x0_it.c                    0x00000000   Number         0  gd32f3x0_it.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\systick.c                        0x00000000   Number         0  systick.o ABSOLUTE
    ..\\CMSIS\\GD\\GD32F3x0\\Source\\system_gd32f3x0.c 0x00000000   Number         0  system_gd32f3x0.o ABSOLUTE
    ..\\HARDWARE\\IIC\\myiic.c               0x00000000   Number         0  myiic.o ABSOLUTE
    ..\\HARDWARE\\RS485.c                    0x00000000   Number         0  rs485.o ABSOLUTE
    ..\\HARDWARE\\UART.c                     0x00000000   Number         0  uart.o ABSOLUTE
    ..\\HARDWARE\\ads1115.c                  0x00000000   Number         0  ads1115.o ABSOLUTE
    ..\\HARDWARE\\crc16.c                    0x00000000   Number         0  crc16.o ABSOLUTE
    ..\\HARDWARE\\led.c                      0x00000000   Number         0  led.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_adc.c      0x00000000   Number         0  gd32f3x0_adc.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_cmp.c      0x00000000   Number         0  gd32f3x0_cmp.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_crc.c      0x00000000   Number         0  gd32f3x0_crc.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_ctc.c      0x00000000   Number         0  gd32f3x0_ctc.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_dbg.c      0x00000000   Number         0  gd32f3x0_dbg.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_dma.c      0x00000000   Number         0  gd32f3x0_dma.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_exti.c     0x00000000   Number         0  gd32f3x0_exti.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_fmc.c      0x00000000   Number         0  gd32f3x0_fmc.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_fwdgt.c    0x00000000   Number         0  gd32f3x0_fwdgt.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_gpio.c     0x00000000   Number         0  gd32f3x0_gpio.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_i2c.c      0x00000000   Number         0  gd32f3x0_i2c.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_misc.c     0x00000000   Number         0  gd32f3x0_misc.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_pmu.c      0x00000000   Number         0  gd32f3x0_pmu.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_rcu.c      0x00000000   Number         0  gd32f3x0_rcu.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_rtc.c      0x00000000   Number         0  gd32f3x0_rtc.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_spi.c      0x00000000   Number         0  gd32f3x0_spi.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_syscfg.c   0x00000000   Number         0  gd32f3x0_syscfg.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_timer.c    0x00000000   Number         0  gd32f3x0_timer.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_usart.c    0x00000000   Number         0  gd32f3x0_usart.o ABSOLUTE
    ..\\Library\\Source\\gd32f3x0_wwdgt.c    0x00000000   Number         0  gd32f3x0_wwdgt.o ABSOLUTE
    ..\\User\\gd32f3x0_it.c                  0x00000000   Number         0  gd32f3x0_it.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\systick.c                      0x00000000   Number         0  systick.o ABSOLUTE
    ..\\gd32f350r_eval.c                     0x00000000   Number         0  gd32f350r_eval.o ABSOLUTE
    ..\gd32f350r_eval.c                      0x00000000   Number         0  gd32f350r_eval.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      336  startup_gd32f3x0.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000150   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000150   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000154   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000158   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000158   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000158   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000160   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000160   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000160   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000160   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000164   Section       36  startup_gd32f3x0.o(.text)
    $v0                                      0x08000164   Number         0  startup_gd32f3x0.o(.text)
    .text                                    0x08000188   Section        0  strlen.o(.text)
    .text                                    0x08000196   Section        0  dadd.o(.text)
    .text                                    0x080002e4   Section        0  dmul.o(.text)
    .text                                    0x080003c8   Section        0  ddiv.o(.text)
    .text                                    0x080004a6   Section        0  dflti.o(.text)
    .text                                    0x080004c8   Section        0  dfixui.o(.text)
    .text                                    0x080004fa   Section        0  f2d.o(.text)
    .text                                    0x08000520   Section       48  cdcmple.o(.text)
    .text                                    0x08000550   Section        0  d2f.o(.text)
    .text                                    0x08000588   Section        0  uidiv.o(.text)
    .text                                    0x080005b4   Section        0  uldiv.o(.text)
    .text                                    0x08000616   Section        0  llshl.o(.text)
    .text                                    0x08000634   Section        0  llushr.o(.text)
    .text                                    0x08000654   Section        0  llsshr.o(.text)
    .text                                    0x08000678   Section        0  iusefp.o(.text)
    .text                                    0x08000678   Section        0  fepilogue.o(.text)
    .text                                    0x080006e6   Section        0  depilogue.o(.text)
    .text                                    0x080007a0   Section        0  dfixul.o(.text)
    .text                                    0x080007d0   Section       48  cdrcmple.o(.text)
    .text                                    0x08000800   Section       36  init.o(.text)
    i.ADS1115_Read_ADC                       0x08000824   Section        0  ads1115.o(i.ADS1115_Read_ADC)
    i.ADS1115_Read_adc                       0x080009d0   Section        0  ads1115.o(i.ADS1115_Read_adc)
    i.ADS1115_Read_average_ADC               0x08000a24   Section        0  ads1115.o(i.ADS1115_Read_average_ADC)
    i.ADS1115_Write                          0x08000b34   Section        0  ads1115.o(i.ADS1115_Write)
    i.BusFault_Handler                       0x08000b74   Section        0  gd32f3x0_it.o(i.BusFault_Handler)
    i.DEBUG                                  0x08000b78   Section        0  uart.o(i.DEBUG)
    i.DebugMon_Handler                       0x08000baa   Section        0  gd32f3x0_it.o(i.DebugMon_Handler)
    i.GD_Led_Config                          0x08000bb0   Section        0  led.o(i.GD_Led_Config)
    i.HardFault_Handler                      0x08000be0   Section        0  gd32f3x0_it.o(i.HardFault_Handler)
    i.IIC_Ack                                0x08000be4   Section        0  myiic.o(i.IIC_Ack)
    i.IIC_Init                               0x08000c3c   Section        0  myiic.o(i.IIC_Init)
    i.IIC_NAck                               0x08000c8c   Section        0  myiic.o(i.IIC_NAck)
    i.IIC_Read_Byte                          0x08000cce   Section        0  myiic.o(i.IIC_Read_Byte)
    i.IIC_ReceiveAck                         0x08000d30   Section        0  ads1115.o(i.IIC_ReceiveAck)
    IIC_ReceiveAck                           0x08000d31   Thumb Code    68  ads1115.o(i.IIC_ReceiveAck)
    i.IIC_SendByte                           0x08000d74   Section        0  myiic.o(i.IIC_SendByte)
    i.IIC_Start                              0x08000dea   Section        0  myiic.o(i.IIC_Start)
    i.IIC_Stop                               0x08000e3e   Section        0  myiic.o(i.IIC_Stop)
    i.IIC_Wait_Ack                           0x08000e80   Section        0  myiic.o(i.IIC_Wait_Ack)
    i.IIC_delay                              0x08000ec6   Section        0  myiic.o(i.IIC_delay)
    IIC_delay                                0x08000ec7   Thumb Code    10  myiic.o(i.IIC_delay)
    i.Led_On                                 0x08000ed0   Section        0  led.o(i.Led_On)
    i.MemManage_Handler                      0x08000ee4   Section        0  gd32f3x0_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08000ee8   Section        0  gd32f3x0_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08000eec   Section        0  gd32f3x0_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08000ef0   Section        0  gd32f3x0_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08000ef4   Section        0  gd32f3x0_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08000ef8   Section        0  system_gd32f3x0.o(i.SystemInit)
    i.USART1_IRQHandler                      0x080011a8   Section        0  rs485.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x0800121c   Section        0  gd32f3x0_it.o(i.UsageFault_Handler)
    i.__0vsnprintf                           0x08001220   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassify                       0x0800124c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_atan                          0x08001280   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x08001558   Section        0  atan2.o(i.__hardfp_atan2)
    i.__hardfp_fabs                          0x08001748   Section        0  fabs.o(i.__hardfp_fabs)
    i.__kernel_poly                          0x0800175c   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x08001854   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08001868   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_underflow                0x08001880   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x080018a0   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080018ae   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080018b0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x080018c0   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x080018c1   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08001a44   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08001a45   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08002120   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08002121   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002144   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002145   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._snputc                                0x08002172   Section        0  printfa.o(i._snputc)
    _snputc                                  0x08002173   Thumb Code    22  printfa.o(i._snputc)
    i._soft_delay_                           0x08002188   Section        0  system_gd32f3x0.o(i._soft_delay_)
    _soft_delay_                             0x08002189   Thumb Code    28  system_gd32f3x0.o(i._soft_delay_)
    i.angle_to_hex16                         0x080021a4   Section        0  main.o(i.angle_to_hex16)
    i.atan                                   0x0800221c   Section        0  atan.o(i.atan)
    i.calculate_elevation_angle_calibrated   0x0800222c   Section        0  main.o(i.calculate_elevation_angle_calibrated)
    i.calculate_high_precision_angle         0x0800240c   Section        0  main.o(i.calculate_high_precision_angle)
    i.delay_ms                               0x08002534   Section        0  systick.o(i.delay_ms)
    i.delay_us                               0x0800258c   Section        0  systick.o(i.delay_us)
    i.elevation_to_hex16                     0x080025e4   Section        0  main.o(i.elevation_to_hex16)
    i.fabs                                   0x0800261c   Section        0  fabs.o(i.fabs)
    i.gpio_af_set                            0x08002634   Section        0  gd32f3x0_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08002692   Section        0  gd32f3x0_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08002696   Section        0  gd32f3x0_gpio.o(i.gpio_bit_set)
    i.gpio_bit_write                         0x0800269a   Section        0  gd32f3x0_gpio.o(i.gpio_bit_write)
    i.gpio_input_bit_get                     0x080026a4   Section        0  gd32f3x0_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x080026b4   Section        0  gd32f3x0_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x08002702   Section        0  gd32f3x0_gpio.o(i.gpio_output_options_set)
    i.high_precision_adc_read                0x08002780   Section        0  main.o(i.high_precision_adc_read)
    i.main                                   0x080027c4   Section        0  main.o(i.main)
    i.nvic_irq_enable                        0x08002908   Section        0  gd32f3x0_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x080029b4   Section        0  gd32f3x0_misc.o(i.nvic_priority_group_set)
    i.nvic_vector_table_set                  0x080029c8   Section        0  gd32f3x0_misc.o(i.nvic_vector_table_set)
    i.rcu_clock_freq_get                     0x080029e0   Section        0  gd32f3x0_rcu.o(i.rcu_clock_freq_get)
    i.rcu_periph_clock_enable                0x08002c7c   Section        0  gd32f3x0_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x08002c9c   Section        0  gd32f3x0_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08002cbc   Section        0  gd32f3x0_rcu.o(i.rcu_periph_reset_enable)
    i.rs485_init                             0x08002cdc   Section        0  rs485.o(i.rs485_init)
    i.rs485_send_data                        0x08002dc8   Section        0  rs485.o(i.rs485_send_data)
    i.rs485_set_rx_mode                      0x08002e10   Section        0  rs485.o(i.rs485_set_rx_mode)
    i.rs485_set_tx_mode                      0x08002e20   Section        0  rs485.o(i.rs485_set_tx_mode)
    i.system_clock_72m_hxtal                 0x08002e30   Section        0  system_gd32f3x0.o(i.system_clock_72m_hxtal)
    system_clock_72m_hxtal                   0x08002e31   Thumb Code   196  system_gd32f3x0.o(i.system_clock_72m_hxtal)
    i.system_clock_config                    0x08002f00   Section        0  system_gd32f3x0.o(i.system_clock_config)
    system_clock_config                      0x08002f01   Thumb Code     8  system_gd32f3x0.o(i.system_clock_config)
    i.systick_clksource_set                  0x08002f08   Section        0  gd32f3x0_misc.o(i.systick_clksource_set)
    i.systick_config                         0x08002f30   Section        0  systick.o(i.systick_config)
    i.uart_send_data                         0x08002fd4   Section        0  uart.o(i.uart_send_data)
    i.usart_baudrate_set                     0x08003014   Section        0  gd32f3x0_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x08003090   Section        0  gd32f3x0_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x0800309a   Section        0  gd32f3x0_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x080030a4   Section        0  gd32f3x0_usart.o(i.usart_deinit)
    i.usart_enable                           0x080030e4   Section        0  gd32f3x0_usart.o(i.usart_enable)
    i.usart_flag_get                         0x080030ee   Section        0  gd32f3x0_usart.o(i.usart_flag_get)
    i.usart_hardware_flow_cts_config         0x0800310c   Section        0  gd32f3x0_usart.o(i.usart_hardware_flow_cts_config)
    i.usart_hardware_flow_rts_config         0x08003124   Section        0  gd32f3x0_usart.o(i.usart_hardware_flow_rts_config)
    i.usart_interrupt_enable                 0x0800313c   Section        0  gd32f3x0_usart.o(i.usart_interrupt_enable)
    i.usart_interrupt_flag_clear             0x08003158   Section        0  gd32f3x0_usart.o(i.usart_interrupt_flag_clear)
    i.usart_interrupt_flag_get               0x08003184   Section        0  gd32f3x0_usart.o(i.usart_interrupt_flag_get)
    i.usart_parity_config                    0x080031bc   Section        0  gd32f3x0_usart.o(i.usart_parity_config)
    i.usart_receive_config                   0x080031d4   Section        0  gd32f3x0_usart.o(i.usart_receive_config)
    i.usart_stop_bit_set                     0x080031e4   Section        0  gd32f3x0_usart.o(i.usart_stop_bit_set)
    i.usart_transmit_config                  0x080031fc   Section        0  gd32f3x0_usart.o(i.usart_transmit_config)
    i.usart_word_length_set                  0x0800320c   Section        0  gd32f3x0_usart.o(i.usart_word_length_set)
    .constdata                               0x08003228   Section      152  atan.o(.constdata)
    atanhi                                   0x08003228   Data          32  atan.o(.constdata)
    atanlo                                   0x08003248   Data          32  atan.o(.constdata)
    aTodd                                    0x08003268   Data          40  atan.o(.constdata)
    aTeven                                   0x08003290   Data          48  atan.o(.constdata)
    .constdata                               0x080032c0   Section        8  qnan.o(.constdata)
    .data                                    0x20000000   Section       54  main.o(.data)
    .data                                    0x20000038   Section        8  systick.o(.data)
    count_1us                                0x20000038   Data           4  systick.o(.data)
    count_1ms                                0x2000003c   Data           4  systick.o(.data)
    .data                                    0x20000040   Section        4  system_gd32f3x0.o(.data)
    .data                                    0x20000044   Section        2  rs485.o(.data)
    .bss                                     0x20000048   Section      148  rs485.o(.bss)
    STACK                                    0x200000e0   Section     1024  startup_gd32f3x0.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000150   Number         0  startup_gd32f3x0.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f3x0.o(RESET)
    __Vectors_End                            0x08000150   Data           0  startup_gd32f3x0.o(RESET)
    __main                                   0x08000151   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000151   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000155   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000159   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000159   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000159   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000159   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000161   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000161   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000165   Thumb Code     8  startup_gd32f3x0.o(.text)
    ADC_CMP_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    CEC_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    DMA_Channel0_IRQHandler                  0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    DMA_Channel1_2_IRQHandler                0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    DMA_Channel3_4_IRQHandler                0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    DMA_Channel5_6_IRQHandler                0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    EXTI0_1_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    EXTI2_3_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    EXTI4_15_IRQHandler                      0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    FMC_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    I2C0_ER_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    I2C0_EV_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    I2C1_ER_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    I2C1_EV_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    LVD_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    RCU_CTC_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    RTC_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    SPI0_IRQHandler                          0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    SPI1_IRQHandler                          0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER0_BRK_UP_TRG_COM_IRQHandler         0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER0_Channel_IRQHandler                0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER13_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER14_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER15_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER16_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER1_IRQHandler                        0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER2_IRQHandler                        0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER5_DAC_IRQHandler                    0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TSI_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    USART0_IRQHandler                        0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    USBFS_IRQHandler                         0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    USBFS_WKUP_IRQHandler                    0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    WWDGT_IRQHandler                         0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    strlen                                   0x08000189   Thumb Code    14  strlen.o(.text)
    __aeabi_dadd                             0x08000197   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080002d9   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080002df   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080002e5   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080003c9   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x080004a7   Thumb Code    34  dflti.o(.text)
    __aeabi_d2uiz                            0x080004c9   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x080004fb   Thumb Code    38  f2d.o(.text)
    __aeabi_cdcmpeq                          0x08000521   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08000521   Thumb Code    48  cdcmple.o(.text)
    __aeabi_d2f                              0x08000551   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000589   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000589   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x080005b5   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08000617   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000617   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000635   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000635   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000655   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000655   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08000679   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000679   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800068b   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x080006e7   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000705   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x080007a1   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x080007d1   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000801   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000801   Thumb Code     0  init.o(.text)
    ADS1115_Read_ADC                         0x08000825   Thumb Code   374  ads1115.o(i.ADS1115_Read_ADC)
    ADS1115_Read_adc                         0x080009d1   Thumb Code    84  ads1115.o(i.ADS1115_Read_adc)
    ADS1115_Read_average_ADC                 0x08000a25   Thumb Code   268  ads1115.o(i.ADS1115_Read_average_ADC)
    ADS1115_Write                            0x08000b35   Thumb Code    64  ads1115.o(i.ADS1115_Write)
    BusFault_Handler                         0x08000b75   Thumb Code     4  gd32f3x0_it.o(i.BusFault_Handler)
    DEBUG                                    0x08000b79   Thumb Code    50  uart.o(i.DEBUG)
    DebugMon_Handler                         0x08000bab   Thumb Code     4  gd32f3x0_it.o(i.DebugMon_Handler)
    GD_Led_Config                            0x08000bb1   Thumb Code    44  led.o(i.GD_Led_Config)
    HardFault_Handler                        0x08000be1   Thumb Code     4  gd32f3x0_it.o(i.HardFault_Handler)
    IIC_Ack                                  0x08000be5   Thumb Code    88  myiic.o(i.IIC_Ack)
    IIC_Init                                 0x08000c3d   Thumb Code    80  myiic.o(i.IIC_Init)
    IIC_NAck                                 0x08000c8d   Thumb Code    66  myiic.o(i.IIC_NAck)
    IIC_Read_Byte                            0x08000ccf   Thumb Code    98  myiic.o(i.IIC_Read_Byte)
    IIC_SendByte                             0x08000d75   Thumb Code   118  myiic.o(i.IIC_SendByte)
    IIC_Start                                0x08000deb   Thumb Code    84  myiic.o(i.IIC_Start)
    IIC_Stop                                 0x08000e3f   Thumb Code    66  myiic.o(i.IIC_Stop)
    IIC_Wait_Ack                             0x08000e81   Thumb Code    70  myiic.o(i.IIC_Wait_Ack)
    Led_On                                   0x08000ed1   Thumb Code    14  led.o(i.Led_On)
    MemManage_Handler                        0x08000ee5   Thumb Code     4  gd32f3x0_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08000ee9   Thumb Code     4  gd32f3x0_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08000eed   Thumb Code     4  gd32f3x0_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08000ef1   Thumb Code     4  gd32f3x0_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08000ef5   Thumb Code     2  gd32f3x0_it.o(i.SysTick_Handler)
    SystemInit                               0x08000ef9   Thumb Code   648  system_gd32f3x0.o(i.SystemInit)
    USART1_IRQHandler                        0x080011a9   Thumb Code    92  rs485.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x0800121d   Thumb Code     4  gd32f3x0_it.o(i.UsageFault_Handler)
    __0vsnprintf                             0x08001221   Thumb Code    40  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08001221   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08001221   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08001221   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08001221   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassify                         0x0800124d   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_atan                            0x08001281   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x08001559   Thumb Code   432  atan2.o(i.__hardfp_atan2)
    __hardfp_fabs                            0x08001749   Thumb Code    20  fabs.o(i.__hardfp_fabs)
    __kernel_poly                            0x0800175d   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x08001855   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08001869   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_underflow                  0x08001881   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x080018a1   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080018af   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080018b1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    angle_to_hex16                           0x080021a5   Thumb Code    94  main.o(i.angle_to_hex16)
    atan                                     0x0800221d   Thumb Code    16  atan.o(i.atan)
    calculate_elevation_angle_calibrated     0x0800222d   Thumb Code   426  main.o(i.calculate_elevation_angle_calibrated)
    calculate_high_precision_angle           0x0800240d   Thumb Code   256  main.o(i.calculate_high_precision_angle)
    delay_ms                                 0x08002535   Thumb Code    84  systick.o(i.delay_ms)
    delay_us                                 0x0800258d   Thumb Code    84  systick.o(i.delay_us)
    elevation_to_hex16                       0x080025e5   Thumb Code    46  main.o(i.elevation_to_hex16)
    fabs                                     0x0800261d   Thumb Code    24  fabs.o(i.fabs)
    gpio_af_set                              0x08002635   Thumb Code    94  gd32f3x0_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08002693   Thumb Code     4  gd32f3x0_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08002697   Thumb Code     4  gd32f3x0_gpio.o(i.gpio_bit_set)
    gpio_bit_write                           0x0800269b   Thumb Code    10  gd32f3x0_gpio.o(i.gpio_bit_write)
    gpio_input_bit_get                       0x080026a5   Thumb Code    16  gd32f3x0_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x080026b5   Thumb Code    78  gd32f3x0_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x08002703   Thumb Code   124  gd32f3x0_gpio.o(i.gpio_output_options_set)
    high_precision_adc_read                  0x08002781   Thumb Code    64  main.o(i.high_precision_adc_read)
    main                                     0x080027c5   Thumb Code   282  main.o(i.main)
    nvic_irq_enable                          0x08002909   Thumb Code   162  gd32f3x0_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x080029b5   Thumb Code    10  gd32f3x0_misc.o(i.nvic_priority_group_set)
    nvic_vector_table_set                    0x080029c9   Thumb Code    16  gd32f3x0_misc.o(i.nvic_vector_table_set)
    rcu_clock_freq_get                       0x080029e1   Thumb Code   628  gd32f3x0_rcu.o(i.rcu_clock_freq_get)
    rcu_periph_clock_enable                  0x08002c7d   Thumb Code    28  gd32f3x0_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08002c9d   Thumb Code    28  gd32f3x0_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08002cbd   Thumb Code    28  gd32f3x0_rcu.o(i.rcu_periph_reset_enable)
    rs485_init                               0x08002cdd   Thumb Code   226  rs485.o(i.rs485_init)
    rs485_send_data                          0x08002dc9   Thumb Code    68  rs485.o(i.rs485_send_data)
    rs485_set_rx_mode                        0x08002e11   Thumb Code    12  rs485.o(i.rs485_set_rx_mode)
    rs485_set_tx_mode                        0x08002e21   Thumb Code    12  rs485.o(i.rs485_set_tx_mode)
    systick_clksource_set                    0x08002f09   Thumb Code    40  gd32f3x0_misc.o(i.systick_clksource_set)
    systick_config                           0x08002f31   Thumb Code   108  systick.o(i.systick_config)
    uart_send_data                           0x08002fd5   Thumb Code    60  uart.o(i.uart_send_data)
    usart_baudrate_set                       0x08003015   Thumb Code   120  gd32f3x0_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x08003091   Thumb Code    10  gd32f3x0_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x0800309b   Thumb Code     8  gd32f3x0_usart.o(i.usart_data_transmit)
    usart_deinit                             0x080030a5   Thumb Code    58  gd32f3x0_usart.o(i.usart_deinit)
    usart_enable                             0x080030e5   Thumb Code    10  gd32f3x0_usart.o(i.usart_enable)
    usart_flag_get                           0x080030ef   Thumb Code    30  gd32f3x0_usart.o(i.usart_flag_get)
    usart_hardware_flow_cts_config           0x0800310d   Thumb Code    24  gd32f3x0_usart.o(i.usart_hardware_flow_cts_config)
    usart_hardware_flow_rts_config           0x08003125   Thumb Code    24  gd32f3x0_usart.o(i.usart_hardware_flow_rts_config)
    usart_interrupt_enable                   0x0800313d   Thumb Code    26  gd32f3x0_usart.o(i.usart_interrupt_enable)
    usart_interrupt_flag_clear               0x08003159   Thumb Code    38  gd32f3x0_usart.o(i.usart_interrupt_flag_clear)
    usart_interrupt_flag_get                 0x08003185   Thumb Code    56  gd32f3x0_usart.o(i.usart_interrupt_flag_get)
    usart_parity_config                      0x080031bd   Thumb Code    24  gd32f3x0_usart.o(i.usart_parity_config)
    usart_receive_config                     0x080031d5   Thumb Code    16  gd32f3x0_usart.o(i.usart_receive_config)
    usart_stop_bit_set                       0x080031e5   Thumb Code    24  gd32f3x0_usart.o(i.usart_stop_bit_set)
    usart_transmit_config                    0x080031fd   Thumb Code    16  gd32f3x0_usart.o(i.usart_transmit_config)
    usart_word_length_set                    0x0800320d   Thumb Code    24  gd32f3x0_usart.o(i.usart_word_length_set)
    __mathlib_zero                           0x080032c0   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x080032c8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080032e8   Number         0  anon$$obj.o(Region$$Table)
    A0_Voltage                               0x20000000   Data           4  main.o(.data)
    A1_Voltage                               0x20000004   Data           4  main.o(.data)
    A2_Voltage                               0x20000008   Data           4  main.o(.data)
    A3_Voltage                               0x2000000c   Data           4  main.o(.data)
    azimuth_angle_deg                        0x20000010   Data           4  main.o(.data)
    elevation_angle_deg                      0x20000014   Data           4  main.o(.data)
    x_signal                                 0x20000018   Data           4  main.o(.data)
    y_signal                                 0x2000001c   Data           4  main.o(.data)
    total_intensity                          0x20000020   Data           4  main.o(.data)
    max_intensity_reference                  0x20000024   Data           4  main.o(.data)
    min_intensity_reference                  0x20000028   Data           4  main.o(.data)
    azimuth_angle_hex                        0x2000002c   Data           2  main.o(.data)
    elevation_angle_hex                      0x2000002e   Data           2  main.o(.data)
    angle_data                               0x20000030   Data           6  main.o(.data)
    SystemCoreClock                          0x20000040   Data           4  system_gd32f3x0.o(.data)
    rs485_rx_index                           0x20000044   Data           1  rs485.o(.data)
    rs485_rxd_flag                           0x20000045   Data           1  rs485.o(.data)
    rs485_rx_buffer                          0x20000048   Data          64  rs485.o(.bss)
    rs485_tx_buffer                          0x20000088   Data          64  rs485.o(.bss)
    Adc_Sensor                               0x200000c8   Data          20  rs485.o(.bss)
    __initial_sp                             0x200004e0   Data           0  startup_gd32f3x0.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000151

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003330, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000032e8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000150   Data   RO          369    RESET               startup_gd32f3x0.o
    0x08000150   0x08000150   0x00000000   Code   RO         4186  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000150   0x08000150   0x00000004   Code   RO         4270    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000154   0x08000154   0x00000004   Code   RO         4273    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000158   0x08000158   0x00000000   Code   RO         4275    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000158   0x08000158   0x00000000   Code   RO         4277    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000158   0x08000158   0x00000008   Code   RO         4278    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000160   0x08000160   0x00000000   Code   RO         4280    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000160   0x08000160   0x00000000   Code   RO         4282    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000160   0x08000160   0x00000004   Code   RO         4271    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000164   0x08000164   0x00000024   Code   RO          370    .text               startup_gd32f3x0.o
    0x08000188   0x08000188   0x0000000e   Code   RO         4191    .text               mc_w.l(strlen.o)
    0x08000196   0x08000196   0x0000014e   Code   RO         4221    .text               mf_w.l(dadd.o)
    0x080002e4   0x080002e4   0x000000e4   Code   RO         4223    .text               mf_w.l(dmul.o)
    0x080003c8   0x080003c8   0x000000de   Code   RO         4225    .text               mf_w.l(ddiv.o)
    0x080004a6   0x080004a6   0x00000022   Code   RO         4227    .text               mf_w.l(dflti.o)
    0x080004c8   0x080004c8   0x00000032   Code   RO         4229    .text               mf_w.l(dfixui.o)
    0x080004fa   0x080004fa   0x00000026   Code   RO         4231    .text               mf_w.l(f2d.o)
    0x08000520   0x08000520   0x00000030   Code   RO         4233    .text               mf_w.l(cdcmple.o)
    0x08000550   0x08000550   0x00000038   Code   RO         4235    .text               mf_w.l(d2f.o)
    0x08000588   0x08000588   0x0000002c   Code   RO         4285    .text               mc_w.l(uidiv.o)
    0x080005b4   0x080005b4   0x00000062   Code   RO         4287    .text               mc_w.l(uldiv.o)
    0x08000616   0x08000616   0x0000001e   Code   RO         4289    .text               mc_w.l(llshl.o)
    0x08000634   0x08000634   0x00000020   Code   RO         4291    .text               mc_w.l(llushr.o)
    0x08000654   0x08000654   0x00000024   Code   RO         4293    .text               mc_w.l(llsshr.o)
    0x08000678   0x08000678   0x00000000   Code   RO         4302    .text               mc_w.l(iusefp.o)
    0x08000678   0x08000678   0x0000006e   Code   RO         4303    .text               mf_w.l(fepilogue.o)
    0x080006e6   0x080006e6   0x000000ba   Code   RO         4305    .text               mf_w.l(depilogue.o)
    0x080007a0   0x080007a0   0x00000030   Code   RO         4313    .text               mf_w.l(dfixul.o)
    0x080007d0   0x080007d0   0x00000030   Code   RO         4315    .text               mf_w.l(cdrcmple.o)
    0x08000800   0x08000800   0x00000024   Code   RO         4317    .text               mc_w.l(init.o)
    0x08000824   0x08000824   0x000001ac   Code   RO         3982    i.ADS1115_Read_ADC  ads1115.o
    0x080009d0   0x080009d0   0x00000054   Code   RO         3983    i.ADS1115_Read_adc  ads1115.o
    0x08000a24   0x08000a24   0x00000110   Code   RO         3984    i.ADS1115_Read_average_ADC  ads1115.o
    0x08000b34   0x08000b34   0x00000040   Code   RO         3985    i.ADS1115_Write     ads1115.o
    0x08000b74   0x08000b74   0x00000004   Code   RO          211    i.BusFault_Handler  gd32f3x0_it.o
    0x08000b78   0x08000b78   0x00000032   Code   RO         3826    i.DEBUG             uart.o
    0x08000baa   0x08000baa   0x00000004   Code   RO          212    i.DebugMon_Handler  gd32f3x0_it.o
    0x08000bae   0x08000bae   0x00000002   PAD
    0x08000bb0   0x08000bb0   0x00000030   Code   RO         3871    i.GD_Led_Config     led.o
    0x08000be0   0x08000be0   0x00000004   Code   RO          213    i.HardFault_Handler  gd32f3x0_it.o
    0x08000be4   0x08000be4   0x00000058   Code   RO         3915    i.IIC_Ack           myiic.o
    0x08000c3c   0x08000c3c   0x00000050   Code   RO         3916    i.IIC_Init          myiic.o
    0x08000c8c   0x08000c8c   0x00000042   Code   RO         3917    i.IIC_NAck          myiic.o
    0x08000cce   0x08000cce   0x00000062   Code   RO         3918    i.IIC_Read_Byte     myiic.o
    0x08000d30   0x08000d30   0x00000044   Code   RO         3986    i.IIC_ReceiveAck    ads1115.o
    0x08000d74   0x08000d74   0x00000076   Code   RO         3919    i.IIC_SendByte      myiic.o
    0x08000dea   0x08000dea   0x00000054   Code   RO         3920    i.IIC_Start         myiic.o
    0x08000e3e   0x08000e3e   0x00000042   Code   RO         3921    i.IIC_Stop          myiic.o
    0x08000e80   0x08000e80   0x00000046   Code   RO         3922    i.IIC_Wait_Ack      myiic.o
    0x08000ec6   0x08000ec6   0x0000000a   Code   RO         3923    i.IIC_delay         myiic.o
    0x08000ed0   0x08000ed0   0x00000014   Code   RO         3874    i.Led_On            led.o
    0x08000ee4   0x08000ee4   0x00000004   Code   RO          214    i.MemManage_Handler  gd32f3x0_it.o
    0x08000ee8   0x08000ee8   0x00000004   Code   RO          215    i.NMI_Handler       gd32f3x0_it.o
    0x08000eec   0x08000eec   0x00000004   Code   RO          216    i.PendSV_Handler    gd32f3x0_it.o
    0x08000ef0   0x08000ef0   0x00000004   Code   RO          217    i.SVC_Handler       gd32f3x0_it.o
    0x08000ef4   0x08000ef4   0x00000002   Code   RO          218    i.SysTick_Handler   gd32f3x0_it.o
    0x08000ef6   0x08000ef6   0x00000002   PAD
    0x08000ef8   0x08000ef8   0x000002b0   Code   RO          317    i.SystemInit        system_gd32f3x0.o
    0x080011a8   0x080011a8   0x00000074   Code   RO         4048    i.USART1_IRQHandler  rs485.o
    0x0800121c   0x0800121c   0x00000004   Code   RO          219    i.UsageFault_Handler  gd32f3x0_it.o
    0x08001220   0x08001220   0x0000002c   Code   RO         4199    i.__0vsnprintf      mc_w.l(printfa.o)
    0x0800124c   0x0800124c   0x00000030   Code   RO         4265    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0800127c   0x0800127c   0x00000004   PAD
    0x08001280   0x08001280   0x000002d8   Code   RO         4237    i.__hardfp_atan     m_wm.l(atan.o)
    0x08001558   0x08001558   0x000001f0   Code   RO         4142    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x08001748   0x08001748   0x00000014   Code   RO         4154    i.__hardfp_fabs     m_wm.l(fabs.o)
    0x0800175c   0x0800175c   0x000000f8   Code   RO         4267    i.__kernel_poly     m_wm.l(poly.o)
    0x08001854   0x08001854   0x00000014   Code   RO         4252    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x08001868   0x08001868   0x00000014   Code   RO         4253    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x0800187c   0x0800187c   0x00000004   PAD
    0x08001880   0x08001880   0x00000020   Code   RO         4257    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x080018a0   0x080018a0   0x0000000e   Code   RO         4321    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080018ae   0x080018ae   0x00000002   Code   RO         4322    i.__scatterload_null  mc_w.l(handlers.o)
    0x080018b0   0x080018b0   0x0000000e   Code   RO         4323    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080018be   0x080018be   0x00000002   PAD
    0x080018c0   0x080018c0   0x00000184   Code   RO         4201    i._fp_digits        mc_w.l(printfa.o)
    0x08001a44   0x08001a44   0x000006dc   Code   RO         4202    i._printf_core      mc_w.l(printfa.o)
    0x08002120   0x08002120   0x00000024   Code   RO         4203    i._printf_post_padding  mc_w.l(printfa.o)
    0x08002144   0x08002144   0x0000002e   Code   RO         4204    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08002172   0x08002172   0x00000016   Code   RO         4205    i._snputc           mc_w.l(printfa.o)
    0x08002188   0x08002188   0x0000001c   Code   RO          318    i._soft_delay_      system_gd32f3x0.o
    0x080021a4   0x080021a4   0x00000078   Code   RO            3    i.angle_to_hex16    main.o
    0x0800221c   0x0800221c   0x00000010   Code   RO         4239    i.atan              m_wm.l(atan.o)
    0x0800222c   0x0800222c   0x000001e0   Code   RO            6    i.calculate_elevation_angle_calibrated  main.o
    0x0800240c   0x0800240c   0x00000128   Code   RO            8    i.calculate_high_precision_angle  main.o
    0x08002534   0x08002534   0x00000058   Code   RO          282    i.delay_ms          systick.o
    0x0800258c   0x0800258c   0x00000058   Code   RO          283    i.delay_us          systick.o
    0x080025e4   0x080025e4   0x00000038   Code   RO           10    i.elevation_to_hex16  main.o
    0x0800261c   0x0800261c   0x00000018   Code   RO         4156    i.fabs              m_wm.l(fabs.o)
    0x08002634   0x08002634   0x0000005e   Code   RO         1574    i.gpio_af_set       gd32f3x0_gpio.o
    0x08002692   0x08002692   0x00000004   Code   RO         1575    i.gpio_bit_reset    gd32f3x0_gpio.o
    0x08002696   0x08002696   0x00000004   Code   RO         1576    i.gpio_bit_set      gd32f3x0_gpio.o
    0x0800269a   0x0800269a   0x0000000a   Code   RO         1578    i.gpio_bit_write    gd32f3x0_gpio.o
    0x080026a4   0x080026a4   0x00000010   Code   RO         1580    i.gpio_input_bit_get  gd32f3x0_gpio.o
    0x080026b4   0x080026b4   0x0000004e   Code   RO         1582    i.gpio_mode_set     gd32f3x0_gpio.o
    0x08002702   0x08002702   0x0000007c   Code   RO         1584    i.gpio_output_options_set  gd32f3x0_gpio.o
    0x0800277e   0x0800277e   0x00000002   PAD
    0x08002780   0x08002780   0x00000044   Code   RO           11    i.high_precision_adc_read  main.o
    0x080027c4   0x080027c4   0x00000144   Code   RO           12    i.main              main.o
    0x08002908   0x08002908   0x000000ac   Code   RO         1879    i.nvic_irq_enable   gd32f3x0_misc.o
    0x080029b4   0x080029b4   0x00000014   Code   RO         1880    i.nvic_priority_group_set  gd32f3x0_misc.o
    0x080029c8   0x080029c8   0x00000018   Code   RO         1881    i.nvic_vector_table_set  gd32f3x0_misc.o
    0x080029e0   0x080029e0   0x0000029c   Code   RO         2079    i.rcu_clock_freq_get  gd32f3x0_rcu.o
    0x08002c7c   0x08002c7c   0x00000020   Code   RO         2099    i.rcu_periph_clock_enable  gd32f3x0_rcu.o
    0x08002c9c   0x08002c9c   0x00000020   Code   RO         2102    i.rcu_periph_reset_disable  gd32f3x0_rcu.o
    0x08002cbc   0x08002cbc   0x00000020   Code   RO         2103    i.rcu_periph_reset_enable  gd32f3x0_rcu.o
    0x08002cdc   0x08002cdc   0x000000ec   Code   RO         4050    i.rs485_init        rs485.o
    0x08002dc8   0x08002dc8   0x00000048   Code   RO         4054    i.rs485_send_data   rs485.o
    0x08002e10   0x08002e10   0x00000010   Code   RO         4055    i.rs485_set_rx_mode  rs485.o
    0x08002e20   0x08002e20   0x00000010   Code   RO         4056    i.rs485_set_tx_mode  rs485.o
    0x08002e30   0x08002e30   0x000000d0   Code   RO          320    i.system_clock_72m_hxtal  system_gd32f3x0.o
    0x08002f00   0x08002f00   0x00000008   Code   RO          321    i.system_clock_config  system_gd32f3x0.o
    0x08002f08   0x08002f08   0x00000028   Code   RO         1884    i.systick_clksource_set  gd32f3x0_misc.o
    0x08002f30   0x08002f30   0x000000a4   Code   RO          284    i.systick_config    systick.o
    0x08002fd4   0x08002fd4   0x00000040   Code   RO         3829    i.uart_send_data    uart.o
    0x08003014   0x08003014   0x0000007c   Code   RO         3338    i.usart_baudrate_set  gd32f3x0_usart.o
    0x08003090   0x08003090   0x0000000a   Code   RO         3344    i.usart_data_receive  gd32f3x0_usart.o
    0x0800309a   0x0800309a   0x00000008   Code   RO         3345    i.usart_data_transmit  gd32f3x0_usart.o
    0x080030a2   0x080030a2   0x00000002   PAD
    0x080030a4   0x080030a4   0x00000040   Code   RO         3346    i.usart_deinit      gd32f3x0_usart.o
    0x080030e4   0x080030e4   0x0000000a   Code   RO         3353    i.usart_enable      gd32f3x0_usart.o
    0x080030ee   0x080030ee   0x0000001e   Code   RO         3355    i.usart_flag_get    gd32f3x0_usart.o
    0x0800310c   0x0800310c   0x00000018   Code   RO         3359    i.usart_hardware_flow_cts_config  gd32f3x0_usart.o
    0x08003124   0x08003124   0x00000018   Code   RO         3360    i.usart_hardware_flow_rts_config  gd32f3x0_usart.o
    0x0800313c   0x0800313c   0x0000001a   Code   RO         3362    i.usart_interrupt_enable  gd32f3x0_usart.o
    0x08003156   0x08003156   0x00000002   PAD
    0x08003158   0x08003158   0x0000002c   Code   RO         3363    i.usart_interrupt_flag_clear  gd32f3x0_usart.o
    0x08003184   0x08003184   0x00000038   Code   RO         3364    i.usart_interrupt_flag_get  gd32f3x0_usart.o
    0x080031bc   0x080031bc   0x00000018   Code   RO         3378    i.usart_parity_config  gd32f3x0_usart.o
    0x080031d4   0x080031d4   0x00000010   Code   RO         3380    i.usart_receive_config  gd32f3x0_usart.o
    0x080031e4   0x080031e4   0x00000018   Code   RO         3399    i.usart_stop_bit_set  gd32f3x0_usart.o
    0x080031fc   0x080031fc   0x00000010   Code   RO         3401    i.usart_transmit_config  gd32f3x0_usart.o
    0x0800320c   0x0800320c   0x00000018   Code   RO         3405    i.usart_word_length_set  gd32f3x0_usart.o
    0x08003224   0x08003224   0x00000004   PAD
    0x08003228   0x08003228   0x00000098   Data   RO         4240    .constdata          m_wm.l(atan.o)
    0x080032c0   0x080032c0   0x00000008   Data   RO         4269    .constdata          m_wm.l(qnan.o)
    0x080032c8   0x080032c8   0x00000020   Data   RO         4319    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080032e8, Size: 0x000004e0, Max: 0x00002000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080032e8   0x00000036   Data   RW           13    .data               main.o
    0x20000036   0x0800331e   0x00000002   PAD
    0x20000038   0x08003320   0x00000008   Data   RW          285    .data               systick.o
    0x20000040   0x08003328   0x00000004   Data   RW          322    .data               system_gd32f3x0.o
    0x20000044   0x0800332c   0x00000002   Data   RW         4059    .data               rs485.o
    0x20000046   0x0800332e   0x00000002   PAD
    0x20000048        -       0x00000094   Zero   RW         4058    .bss                rs485.o
    0x200000dc   0x0800332e   0x00000004   PAD
    0x200000e0        -       0x00000400   Zero   RW          367    STACK               startup_gd32f3x0.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       916         64          0          0          0       4345   ads1115.o
       330          0          0          0          0       5318   gd32f3x0_gpio.o
        34          0          0          0          0       4790   gd32f3x0_it.o
       256         28          0          0          0       2804   gd32f3x0_misc.o
       764         60          0          0          0       3988   gd32f3x0_rcu.o
       524         16          0          0          0      11072   gd32f3x0_usart.o
        68         10          0          0          0       1089   led.o
      1344        176          0         54          0      48522   main.o
       680          0          0          0          0       5429   myiic.o
       456         46          0          2        148       4863   rs485.o
        36          8        336          0       1024        932   startup_gd32f3x0.o
       932         52          0          4          0       4459   system_gd32f3x0.o
       340         64          0          8          0       2087   systick.o
       114          4          0          0          0       2405   uart.o

    ----------------------------------------------------------------------
      6808        <USER>        <GROUP>         72       1176     102103   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          0          4          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       744        106        152          0          0        352   atan.o
       496         64          0          0          0        192   atan2.o
        72          8          0          0          0        372   dunder.o
        44          0          0          0          0        248   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
      2292         84          0          0          0        516   printfa.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      5696        <USER>        <GROUP>          0          0       3668   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1652        178        160          0          0       1440   m_wm.l
      2632        100          0          0          0       1028   mc_w.l
      1402          0          0          0          0       1200   mf_w.l

    ----------------------------------------------------------------------
      5696        <USER>        <GROUP>          0          0       3668   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12504        806        528         72       1176      97031   Grand Totals
     12504        806        528         72       1176      97031   ELF Image Totals
     12504        806        528         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13032 (  12.73kB)
    Total RW  Size (RW Data + ZI Data)              1248 (   1.22kB)
    Total ROM Size (Code + RO Data + RW Data)      13104 (  12.80kB)

==============================================================================

