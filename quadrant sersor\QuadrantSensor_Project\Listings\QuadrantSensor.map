Component: ARM Compiler 6.12 Tool: armlink [5d624300]

==============================================================================

Section Cross References

    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_osci_on) for rcu_osci_on
    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_osci_stab_wait) for rcu_osci_stab_wait
    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_pll_preselection_config) for rcu_pll_preselection_config
    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_pll_config) for rcu_pll_config
    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_ahb_clock_config) for rcu_ahb_clock_config
    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_apb1_clock_config) for rcu_apb1_clock_config
    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_apb2_clock_config) for rcu_apb2_clock_config
    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_system_clock_source_config) for rcu_system_clock_source_config
    main.o(.text.system_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_system_clock_source_get) for rcu_system_clock_source_get
    main.o(.ARM.exidx.text.system_clock_config) refers to main.o(.text.system_clock_config) for [Anonymous Symbol]
    main.o(.text.board_init) refers to cm1103_driver.o(.text.cm1103_init) for cm1103_init
    main.o(.text.board_init) refers to systick.o(.text.delay_1ms) for delay_1ms
    main.o(.ARM.exidx.text.board_init) refers to main.o(.text.board_init) for [Anonymous Symbol]
    main.o(.text.main) refers to main.o(.text.system_clock_config) for system_clock_config
    main.o(.text.main) refers to systick.o(.text.systick_config) for systick_config
    main.o(.text.main) refers to rs485_comm.o(.text.rs485_init) for rs485_init
    main.o(.text.main) refers to main.o(.text.board_init) for board_init
    main.o(.text.main) refers to systick.o(.text.delay_1ms) for delay_1ms
    main.o(.text.main) refers to rs485_comm.o(.text.rs485_send_system_ready) for rs485_send_system_ready
    main.o(.text.main) refers to cm1103_driver.o(.text.cm1103_update_continuous_data) for cm1103_update_continuous_data
    main.o(.text.main) refers to rs485_comm.o(.text.rs485_get_received_command) for rs485_get_received_command
    main.o(.text.main) refers to rs485_comm.o(.text.rs485_process_command) for rs485_process_command
    main.o(.text.main) refers to systick.o(.text.systick_get_tick) for systick_get_tick
    main.o(.text.main) refers to cm1103_driver.o(.text.cm1103_read_all_channels) for cm1103_read_all_channels
    main.o(.text.main) refers to rs485_comm.o(.text.rs485_send_quadrant_data_auto) for rs485_send_quadrant_data_auto
    main.o(.text.main) refers to cm1103_driver.o(.text.cm1103_is_cached_data_valid) for cm1103_is_cached_data_valid
    main.o(.text.main) refers to rs485_comm.o(.text.rs485_send_error_response) for rs485_send_error_response
    main.o(.text.main) refers to rs485_comm.o(.bss..L_MergedGlobals) for g_rs485_frame_received
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    gd32f3x0_it.o(.ARM.exidx.text.NMI_Handler) refers to gd32f3x0_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.ARM.exidx.text.HardFault_Handler) refers to gd32f3x0_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.ARM.exidx.text.MemManage_Handler) refers to gd32f3x0_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.ARM.exidx.text.BusFault_Handler) refers to gd32f3x0_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.ARM.exidx.text.UsageFault_Handler) refers to gd32f3x0_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.ARM.exidx.text.SVC_Handler) refers to gd32f3x0_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.ARM.exidx.text.DebugMon_Handler) refers to gd32f3x0_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.ARM.exidx.text.PendSV_Handler) refers to gd32f3x0_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.text.SysTick_Handler) refers to systick.o(.text.delay_decrement) for delay_decrement
    gd32f3x0_it.o(.ARM.exidx.text.SysTick_Handler) refers to gd32f3x0_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    gd32f3x0_it.o(.text.USART1_IRQHandler) refers to rs485_comm.o(.text.rs485_uart_irq_handler) for rs485_uart_irq_handler
    gd32f3x0_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to gd32f3x0_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    systick.o(.text.systick_config) refers to system_gd32f3x0.o(.data.SystemCoreClock) for SystemCoreClock
    systick.o(.ARM.exidx.text.systick_config) refers to systick.o(.text.systick_config) for [Anonymous Symbol]
    systick.o(.text.delay_1ms) refers to systick.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    systick.o(.ARM.exidx.text.delay_1ms) refers to systick.o(.text.delay_1ms) for [Anonymous Symbol]
    systick.o(.text.delay_decrement) refers to systick.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    systick.o(.ARM.exidx.text.delay_decrement) refers to systick.o(.text.delay_decrement) for [Anonymous Symbol]
    systick.o(.text.systick_get_tick) refers to systick.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    systick.o(.ARM.exidx.text.systick_get_tick) refers to systick.o(.text.systick_get_tick) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_i2c_scan) refers to soft_i2c.o(.text.soft_i2c_read_reg) for soft_i2c_read_reg
    cm1103_driver.o(.ARM.exidx.text.cm1103_i2c_scan) refers to cm1103_driver.o(.text.cm1103_i2c_scan) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_init) refers to soft_i2c.o(.text.soft_i2c_init) for soft_i2c_init
    cm1103_driver.o(.text.cm1103_init) refers to systick.o(.text.delay_1ms) for delay_1ms
    cm1103_driver.o(.text.cm1103_init) refers to cm1103_driver.o(.text.cm1103_i2c_scan) for cm1103_i2c_scan
    cm1103_driver.o(.text.cm1103_init) refers to soft_i2c.o(.text.soft_i2c_read_reg) for soft_i2c_read_reg
    cm1103_driver.o(.text.cm1103_init) refers to cm1103_driver.o(.text.cm1103_write_register) for cm1103_write_register
    cm1103_driver.o(.text.cm1103_init) refers to cm1103_driver.o(.text.cm1103_read_register) for cm1103_read_register
    cm1103_driver.o(.text.cm1103_init) refers to cm1103_driver.o(.text.cm1103_config_channel) for cm1103_config_channel
    cm1103_driver.o(.text.cm1103_init) refers to cm1103_driver.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    cm1103_driver.o(.ARM.exidx.text.cm1103_init) refers to cm1103_driver.o(.text.cm1103_init) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_write_register) refers to soft_i2c.o(.text.soft_i2c_write_reg) for soft_i2c_write_reg
    cm1103_driver.o(.ARM.exidx.text.cm1103_write_register) refers to cm1103_driver.o(.text.cm1103_write_register) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_read_register) refers to soft_i2c.o(.text.soft_i2c_read_reg) for soft_i2c_read_reg
    cm1103_driver.o(.ARM.exidx.text.cm1103_read_register) refers to cm1103_driver.o(.text.cm1103_read_register) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_config_channel) refers to cm1103_driver.o(.text.cm1103_write_register) for cm1103_write_register
    cm1103_driver.o(.text.cm1103_config_channel) refers to cm1103_driver.o(.text.cm1103_read_register) for cm1103_read_register
    cm1103_driver.o(.text.cm1103_config_channel) refers to cm1103_driver.o(.data.current_config) for [Anonymous Symbol]
    cm1103_driver.o(.ARM.exidx.text.cm1103_config_channel) refers to cm1103_driver.o(.text.cm1103_config_channel) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_update_continuous_data) refers to systick.o(.text.systick_get_tick) for systick_get_tick
    cm1103_driver.o(.text.cm1103_update_continuous_data) refers to cm1103_driver.o(.text.cm1103_read_conversion) for cm1103_read_conversion
    cm1103_driver.o(.text.cm1103_update_continuous_data) refers to cm1103_driver.o(.text.cm1103_reset_cache) for cm1103_reset_cache
    cm1103_driver.o(.text.cm1103_update_continuous_data) refers to cm1103_driver.o(.text.cm1103_config_channel) for cm1103_config_channel
    cm1103_driver.o(.text.cm1103_update_continuous_data) refers to systick.o(.text.delay_1ms) for delay_1ms
    cm1103_driver.o(.text.cm1103_update_continuous_data) refers to cm1103_driver.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_update_continuous_data) refers to cm1103_driver.o(.data.cm1103_update_continuous_data.last_data) for [Anonymous Symbol]
    cm1103_driver.o(.ARM.exidx.text.cm1103_update_continuous_data) refers to cm1103_driver.o(.text.cm1103_update_continuous_data) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_read_conversion) refers to cm1103_driver.o(.text.cm1103_read_register) for cm1103_read_register
    cm1103_driver.o(.ARM.exidx.text.cm1103_read_conversion) refers to cm1103_driver.o(.text.cm1103_read_conversion) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_reset_cache) refers to cm1103_driver.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    cm1103_driver.o(.ARM.exidx.text.cm1103_reset_cache) refers to cm1103_driver.o(.text.cm1103_reset_cache) for [Anonymous Symbol]
    cm1103_driver.o(.ARM.exidx.text.cm1103_adc_to_voltage) refers to cm1103_driver.o(.text.cm1103_adc_to_voltage) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_get_cache_status) refers to systick.o(.text.systick_get_tick) for systick_get_tick
    cm1103_driver.o(.text.cm1103_get_cache_status) refers to cm1103_driver.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    cm1103_driver.o(.ARM.exidx.text.cm1103_get_cache_status) refers to cm1103_driver.o(.text.cm1103_get_cache_status) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_is_cached_data_valid) refers to systick.o(.text.systick_get_tick) for systick_get_tick
    cm1103_driver.o(.text.cm1103_is_cached_data_valid) refers to cm1103_driver.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    cm1103_driver.o(.ARM.exidx.text.cm1103_is_cached_data_valid) refers to cm1103_driver.o(.text.cm1103_is_cached_data_valid) for [Anonymous Symbol]
    cm1103_driver.o(.text.cm1103_read_all_channels) refers to cm1103_driver.o(.text.cm1103_is_cached_data_valid) for cm1103_is_cached_data_valid
    cm1103_driver.o(.text.cm1103_read_all_channels) refers to systick.o(.text.systick_get_tick) for systick_get_tick
    cm1103_driver.o(.text.cm1103_read_all_channels) refers to cm1103_driver.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    cm1103_driver.o(.ARM.exidx.text.cm1103_read_all_channels) refers to cm1103_driver.o(.text.cm1103_read_all_channels) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_init) refers to gd32f3x0_rcu.o(.text.rcu_periph_clock_enable) for rcu_periph_clock_enable
    soft_i2c.o(.text.soft_i2c_init) refers to gd32f3x0_gpio.o(.text.gpio_mode_set) for gpio_mode_set
    soft_i2c.o(.text.soft_i2c_init) refers to gd32f3x0_gpio.o(.text.gpio_output_options_set) for gpio_output_options_set
    soft_i2c.o(.text.soft_i2c_init) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    soft_i2c.o(.text.soft_i2c_init) refers to soft_i2c.o(.text.soft_i2c_delay) for soft_i2c_delay
    soft_i2c.o(.ARM.exidx.text.soft_i2c_init) refers to soft_i2c.o(.text.soft_i2c_init) for [Anonymous Symbol]
    soft_i2c.o(.ARM.exidx.text.soft_i2c_delay) refers to soft_i2c.o(.text.soft_i2c_delay) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_start) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    soft_i2c.o(.text.soft_i2c_start) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    soft_i2c.o(.text.soft_i2c_start) refers to soft_i2c.o(.text.soft_i2c_delay) for soft_i2c_delay
    soft_i2c.o(.ARM.exidx.text.soft_i2c_start) refers to soft_i2c.o(.text.soft_i2c_start) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_stop) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    soft_i2c.o(.text.soft_i2c_stop) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    soft_i2c.o(.text.soft_i2c_stop) refers to soft_i2c.o(.text.soft_i2c_delay) for soft_i2c_delay
    soft_i2c.o(.ARM.exidx.text.soft_i2c_stop) refers to soft_i2c.o(.text.soft_i2c_stop) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_send_ack) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    soft_i2c.o(.text.soft_i2c_send_ack) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    soft_i2c.o(.text.soft_i2c_send_ack) refers to soft_i2c.o(.text.soft_i2c_delay) for soft_i2c_delay
    soft_i2c.o(.ARM.exidx.text.soft_i2c_send_ack) refers to soft_i2c.o(.text.soft_i2c_send_ack) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_send_nack) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    soft_i2c.o(.text.soft_i2c_send_nack) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    soft_i2c.o(.text.soft_i2c_send_nack) refers to soft_i2c.o(.text.soft_i2c_delay) for soft_i2c_delay
    soft_i2c.o(.ARM.exidx.text.soft_i2c_send_nack) refers to soft_i2c.o(.text.soft_i2c_send_nack) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_wait_ack) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    soft_i2c.o(.text.soft_i2c_wait_ack) refers to soft_i2c.o(.text.soft_i2c_delay) for soft_i2c_delay
    soft_i2c.o(.text.soft_i2c_wait_ack) refers to gd32f3x0_gpio.o(.text.gpio_input_bit_get) for gpio_input_bit_get
    soft_i2c.o(.text.soft_i2c_wait_ack) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    soft_i2c.o(.ARM.exidx.text.soft_i2c_wait_ack) refers to soft_i2c.o(.text.soft_i2c_wait_ack) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_send_byte) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    soft_i2c.o(.text.soft_i2c_send_byte) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    soft_i2c.o(.text.soft_i2c_send_byte) refers to soft_i2c.o(.text.soft_i2c_delay) for soft_i2c_delay
    soft_i2c.o(.ARM.exidx.text.soft_i2c_send_byte) refers to soft_i2c.o(.text.soft_i2c_send_byte) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_read_byte) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    soft_i2c.o(.text.soft_i2c_read_byte) refers to soft_i2c.o(.text.soft_i2c_delay) for soft_i2c_delay
    soft_i2c.o(.text.soft_i2c_read_byte) refers to gd32f3x0_gpio.o(.text.gpio_input_bit_get) for gpio_input_bit_get
    soft_i2c.o(.text.soft_i2c_read_byte) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    soft_i2c.o(.ARM.exidx.text.soft_i2c_read_byte) refers to soft_i2c.o(.text.soft_i2c_read_byte) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_write_reg) refers to soft_i2c.o(.text.soft_i2c_start) for soft_i2c_start
    soft_i2c.o(.text.soft_i2c_write_reg) refers to soft_i2c.o(.text.soft_i2c_send_byte) for soft_i2c_send_byte
    soft_i2c.o(.text.soft_i2c_write_reg) refers to soft_i2c.o(.text.soft_i2c_wait_ack) for soft_i2c_wait_ack
    soft_i2c.o(.text.soft_i2c_write_reg) refers to soft_i2c.o(.text.soft_i2c_stop) for soft_i2c_stop
    soft_i2c.o(.ARM.exidx.text.soft_i2c_write_reg) refers to soft_i2c.o(.text.soft_i2c_write_reg) for [Anonymous Symbol]
    soft_i2c.o(.text.soft_i2c_read_reg) refers to soft_i2c.o(.text.soft_i2c_start) for soft_i2c_start
    soft_i2c.o(.text.soft_i2c_read_reg) refers to soft_i2c.o(.text.soft_i2c_send_byte) for soft_i2c_send_byte
    soft_i2c.o(.text.soft_i2c_read_reg) refers to soft_i2c.o(.text.soft_i2c_wait_ack) for soft_i2c_wait_ack
    soft_i2c.o(.text.soft_i2c_read_reg) refers to soft_i2c.o(.text.soft_i2c_read_byte) for soft_i2c_read_byte
    soft_i2c.o(.text.soft_i2c_read_reg) refers to soft_i2c.o(.text.soft_i2c_send_ack) for soft_i2c_send_ack
    soft_i2c.o(.text.soft_i2c_read_reg) refers to soft_i2c.o(.text.soft_i2c_send_nack) for soft_i2c_send_nack
    soft_i2c.o(.text.soft_i2c_read_reg) refers to soft_i2c.o(.text.soft_i2c_stop) for soft_i2c_stop
    soft_i2c.o(.ARM.exidx.text.soft_i2c_read_reg) refers to soft_i2c.o(.text.soft_i2c_read_reg) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_gpio.o(.text.gpio_output_options_set) for gpio_output_options_set
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_gpio.o(.text.gpio_af_set) for gpio_af_set
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_deinit) for usart_deinit
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_baudrate_set) for usart_baudrate_set
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_word_length_set) for usart_word_length_set
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_stop_bit_set) for usart_stop_bit_set
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_parity_config) for usart_parity_config
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_receive_config) for usart_receive_config
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_transmit_config) for usart_transmit_config
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_interrupt_enable) for usart_interrupt_enable
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_misc.o(.text.nvic_irq_enable) for nvic_irq_enable
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_usart.o(.text.usart_enable) for usart_enable
    rs485_comm.o(.text.rs485_init) refers to systick.o(.text.delay_1ms) for delay_1ms
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_rcu.o(.text.rcu_periph_clock_enable) for rcu_periph_clock_enable
    rs485_comm.o(.text.rs485_init) refers to gd32f3x0_gpio.o(.text.gpio_mode_set) for gpio_mode_set
    rs485_comm.o(.text.rs485_init) refers to rs485_comm.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_init) refers to rs485_comm.o(.data.g_rs485_tx_complete) for g_rs485_tx_complete
    rs485_comm.o(.ARM.exidx.text.rs485_init) refers to rs485_comm.o(.text.rs485_init) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_send_data) refers to memcpya.o(.text) for __aeabi_memcpy
    rs485_comm.o(.text.rs485_send_data) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for gpio_bit_set
    rs485_comm.o(.text.rs485_send_data) refers to systick.o(.text.delay_1ms) for delay_1ms
    rs485_comm.o(.text.rs485_send_data) refers to gd32f3x0_usart.o(.text.usart_data_transmit) for usart_data_transmit
    rs485_comm.o(.text.rs485_send_data) refers to gd32f3x0_usart.o(.text.usart_flag_get) for usart_flag_get
    rs485_comm.o(.text.rs485_send_data) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for gpio_bit_reset
    rs485_comm.o(.text.rs485_send_data) refers to rs485_comm.o(.bss.tx_buffer) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_send_data) refers to rs485_comm.o(.data.g_rs485_tx_complete) for g_rs485_tx_complete
    rs485_comm.o(.ARM.exidx.text.rs485_send_data) refers to rs485_comm.o(.text.rs485_send_data) for [Anonymous Symbol]
    rs485_comm.o(.ARM.exidx.text.rs485_calculate_checksum) refers to rs485_comm.o(.text.rs485_calculate_checksum) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_get_received_command) refers to rs485_comm.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    rs485_comm.o(.ARM.exidx.text.rs485_get_received_command) refers to rs485_comm.o(.text.rs485_get_received_command) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_process_received_byte) refers to systick.o(.text.systick_get_tick) for systick_get_tick
    rs485_comm.o(.text.rs485_process_received_byte) refers to rs485_comm.o(.text.rs485_calculate_checksum) for rs485_calculate_checksum
    rs485_comm.o(.text.rs485_process_received_byte) refers to rs485_comm.o(.bss..L_MergedGlobals) for [Anonymous Symbol]
    rs485_comm.o(.ARM.exidx.text.rs485_process_received_byte) refers to rs485_comm.o(.text.rs485_process_received_byte) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_process_command) refers to systick.o(.text.delay_1ms) for delay_1ms
    rs485_comm.o(.text.rs485_process_command) refers to rs485_comm.o(.text.rs485_send_heartbeat_response) for rs485_send_heartbeat_response
    rs485_comm.o(.text.rs485_process_command) refers to rs485_comm.o(.text.rs485_send_quadrant_data_response) for rs485_send_quadrant_data_response
    rs485_comm.o(.text.rs485_process_command) refers to rs485_comm.o(.text.rs485_send_error_response) for rs485_send_error_response
    rs485_comm.o(.ARM.exidx.text.rs485_process_command) refers to rs485_comm.o(.text.rs485_process_command) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_send_heartbeat_response) refers to rs485_comm.o(.text.rs485_calculate_checksum) for rs485_calculate_checksum
    rs485_comm.o(.text.rs485_send_heartbeat_response) refers to rs485_comm.o(.text.rs485_send_data) for rs485_send_data
    rs485_comm.o(.ARM.exidx.text.rs485_send_heartbeat_response) refers to rs485_comm.o(.text.rs485_send_heartbeat_response) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_send_quadrant_data_response) refers to cm1103_driver.o(.text.cm1103_read_all_channels) for cm1103_read_all_channels
    rs485_comm.o(.text.rs485_send_quadrant_data_response) refers to rs485_comm.o(.text.rs485_calculate_checksum) for rs485_calculate_checksum
    rs485_comm.o(.text.rs485_send_quadrant_data_response) refers to rs485_comm.o(.text.rs485_send_data) for rs485_send_data
    rs485_comm.o(.text.rs485_send_quadrant_data_response) refers to rs485_comm.o(.rodata..L__const.rs485_send_quadrant_data_response.response) for [Anonymous Symbol]
    rs485_comm.o(.ARM.exidx.text.rs485_send_quadrant_data_response) refers to rs485_comm.o(.text.rs485_send_quadrant_data_response) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_send_error_response) refers to rs485_comm.o(.text.rs485_calculate_checksum) for rs485_calculate_checksum
    rs485_comm.o(.text.rs485_send_error_response) refers to rs485_comm.o(.text.rs485_send_data) for rs485_send_data
    rs485_comm.o(.ARM.exidx.text.rs485_send_error_response) refers to rs485_comm.o(.text.rs485_send_error_response) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_send_system_ready) refers to rs485_comm.o(.text.rs485_calculate_checksum) for rs485_calculate_checksum
    rs485_comm.o(.text.rs485_send_system_ready) refers to rs485_comm.o(.text.rs485_send_data) for rs485_send_data
    rs485_comm.o(.ARM.exidx.text.rs485_send_system_ready) refers to rs485_comm.o(.text.rs485_send_system_ready) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_send_quadrant_data_auto) refers to rs485_comm.o(.text.rs485_calculate_checksum) for rs485_calculate_checksum
    rs485_comm.o(.text.rs485_send_quadrant_data_auto) refers to rs485_comm.o(.text.rs485_send_data) for rs485_send_data
    rs485_comm.o(.text.rs485_send_quadrant_data_auto) refers to rs485_comm.o(.rodata..L__const.rs485_send_quadrant_data_auto.response) for [Anonymous Symbol]
    rs485_comm.o(.ARM.exidx.text.rs485_send_quadrant_data_auto) refers to rs485_comm.o(.text.rs485_send_quadrant_data_auto) for [Anonymous Symbol]
    rs485_comm.o(.text.rs485_uart_irq_handler) refers to gd32f3x0_usart.o(.text.usart_interrupt_flag_get) for usart_interrupt_flag_get
    rs485_comm.o(.text.rs485_uart_irq_handler) refers to gd32f3x0_usart.o(.text.usart_data_receive) for usart_data_receive
    rs485_comm.o(.text.rs485_uart_irq_handler) refers to rs485_comm.o(.text.rs485_process_received_byte) for rs485_process_received_byte
    rs485_comm.o(.ARM.exidx.text.rs485_uart_irq_handler) refers to rs485_comm.o(.text.rs485_uart_irq_handler) for [Anonymous Symbol]
    system_gd32f3x0.o(.text.SystemInit) refers to gd32f3x0_misc.o(.text.nvic_vector_table_set) for nvic_vector_table_set
    system_gd32f3x0.o(.ARM.exidx.text.SystemInit) refers to system_gd32f3x0.o(.text.SystemInit) for [Anonymous Symbol]
    system_gd32f3x0.o(.text.SystemCoreClockUpdate) refers to system_gd32f3x0.o(.data.SystemCoreClock) for SystemCoreClock
    system_gd32f3x0.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_gd32f3x0.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    system_gd32f3x0.o(.ARM.exidx.text.gd32f3x0_firmware_version_get) refers to system_gd32f3x0.o(.text.gd32f3x0_firmware_version_get) for [Anonymous Symbol]
    gd32f3x0_adc.o(.text.adc_deinit) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_adc.o(.text.adc_deinit) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_adc.o(.ARM.exidx.text.adc_deinit) refers to gd32f3x0_adc.o(.text.adc_deinit) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_enable) refers to gd32f3x0_adc.o(.text.adc_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_disable) refers to gd32f3x0_adc.o(.text.adc_disable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_calibration_enable) refers to gd32f3x0_adc.o(.text.adc_calibration_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_dma_mode_enable) refers to gd32f3x0_adc.o(.text.adc_dma_mode_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_dma_mode_disable) refers to gd32f3x0_adc.o(.text.adc_dma_mode_disable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_tempsensor_vrefint_enable) refers to gd32f3x0_adc.o(.text.adc_tempsensor_vrefint_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_tempsensor_vrefint_disable) refers to gd32f3x0_adc.o(.text.adc_tempsensor_vrefint_disable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_vbat_enable) refers to gd32f3x0_adc.o(.text.adc_vbat_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_vbat_disable) refers to gd32f3x0_adc.o(.text.adc_vbat_disable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_discontinuous_mode_config) refers to gd32f3x0_adc.o(.text.adc_discontinuous_mode_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_special_function_config) refers to gd32f3x0_adc.o(.text.adc_special_function_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_data_alignment_config) refers to gd32f3x0_adc.o(.text.adc_data_alignment_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_channel_length_config) refers to gd32f3x0_adc.o(.text.adc_channel_length_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_regular_channel_config) refers to gd32f3x0_adc.o(.text.adc_regular_channel_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_inserted_channel_config) refers to gd32f3x0_adc.o(.text.adc_inserted_channel_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_inserted_channel_offset_config) refers to gd32f3x0_adc.o(.text.adc_inserted_channel_offset_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_external_trigger_config) refers to gd32f3x0_adc.o(.text.adc_external_trigger_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_external_trigger_source_config) refers to gd32f3x0_adc.o(.text.adc_external_trigger_source_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_software_trigger_enable) refers to gd32f3x0_adc.o(.text.adc_software_trigger_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_regular_data_read) refers to gd32f3x0_adc.o(.text.adc_regular_data_read) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_inserted_data_read) refers to gd32f3x0_adc.o(.text.adc_inserted_data_read) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_watchdog_single_channel_enable) refers to gd32f3x0_adc.o(.text.adc_watchdog_single_channel_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_watchdog_group_channel_enable) refers to gd32f3x0_adc.o(.text.adc_watchdog_group_channel_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_watchdog_disable) refers to gd32f3x0_adc.o(.text.adc_watchdog_disable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_watchdog_threshold_config) refers to gd32f3x0_adc.o(.text.adc_watchdog_threshold_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_resolution_config) refers to gd32f3x0_adc.o(.text.adc_resolution_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_oversample_mode_config) refers to gd32f3x0_adc.o(.text.adc_oversample_mode_config) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_oversample_mode_enable) refers to gd32f3x0_adc.o(.text.adc_oversample_mode_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_oversample_mode_disable) refers to gd32f3x0_adc.o(.text.adc_oversample_mode_disable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_flag_get) refers to gd32f3x0_adc.o(.text.adc_flag_get) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_flag_clear) refers to gd32f3x0_adc.o(.text.adc_flag_clear) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_interrupt_enable) refers to gd32f3x0_adc.o(.text.adc_interrupt_enable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_interrupt_disable) refers to gd32f3x0_adc.o(.text.adc_interrupt_disable) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_interrupt_flag_get) refers to gd32f3x0_adc.o(.text.adc_interrupt_flag_get) for [Anonymous Symbol]
    gd32f3x0_adc.o(.ARM.exidx.text.adc_interrupt_flag_clear) refers to gd32f3x0_adc.o(.text.adc_interrupt_flag_clear) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.text.gpio_deinit) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_gpio.o(.text.gpio_deinit) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_deinit) refers to gd32f3x0_gpio.o(.text.gpio_deinit) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_mode_set) refers to gd32f3x0_gpio.o(.text.gpio_mode_set) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_output_options_set) refers to gd32f3x0_gpio.o(.text.gpio_output_options_set) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_bit_set) refers to gd32f3x0_gpio.o(.text.gpio_bit_set) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_bit_reset) refers to gd32f3x0_gpio.o(.text.gpio_bit_reset) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_bit_write) refers to gd32f3x0_gpio.o(.text.gpio_bit_write) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_port_write) refers to gd32f3x0_gpio.o(.text.gpio_port_write) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_input_bit_get) refers to gd32f3x0_gpio.o(.text.gpio_input_bit_get) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_input_port_get) refers to gd32f3x0_gpio.o(.text.gpio_input_port_get) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_output_bit_get) refers to gd32f3x0_gpio.o(.text.gpio_output_bit_get) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_output_port_get) refers to gd32f3x0_gpio.o(.text.gpio_output_port_get) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_af_set) refers to gd32f3x0_gpio.o(.text.gpio_af_set) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_pin_lock) refers to gd32f3x0_gpio.o(.text.gpio_pin_lock) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_bit_toggle) refers to gd32f3x0_gpio.o(.text.gpio_bit_toggle) for [Anonymous Symbol]
    gd32f3x0_gpio.o(.ARM.exidx.text.gpio_port_toggle) refers to gd32f3x0_gpio.o(.text.gpio_port_toggle) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_deinit) refers to gd32f3x0_rcu.o(.text.rcu_deinit) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_clock_enable) refers to gd32f3x0_rcu.o(.text.rcu_periph_clock_enable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_clock_disable) refers to gd32f3x0_rcu.o(.text.rcu_periph_clock_disable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_clock_sleep_enable) refers to gd32f3x0_rcu.o(.text.rcu_periph_clock_sleep_enable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_clock_sleep_disable) refers to gd32f3x0_rcu.o(.text.rcu_periph_clock_sleep_disable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_reset_enable) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_enable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_reset_disable) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_disable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_bkp_reset_enable) refers to gd32f3x0_rcu.o(.text.rcu_bkp_reset_enable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_bkp_reset_disable) refers to gd32f3x0_rcu.o(.text.rcu_bkp_reset_disable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_system_clock_source_config) refers to gd32f3x0_rcu.o(.text.rcu_system_clock_source_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_system_clock_source_get) refers to gd32f3x0_rcu.o(.text.rcu_system_clock_source_get) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_ahb_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_ahb_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_apb1_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_apb1_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_apb2_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_apb2_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_adc_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_adc_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_usbfs_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_usbfs_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_ckout_config) refers to gd32f3x0_rcu.o(.text.rcu_ckout_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_pll_preselection_config) refers to gd32f3x0_rcu.o(.text.rcu_pll_preselection_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_pll_config) refers to gd32f3x0_rcu.o(.text.rcu_pll_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_usart_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_usart_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_cec_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_cec_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_rtc_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_rtc_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_ck48m_clock_config) refers to gd32f3x0_rcu.o(.text.rcu_ck48m_clock_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_hxtal_prediv_config) refers to gd32f3x0_rcu.o(.text.rcu_hxtal_prediv_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_lxtal_drive_capability_config) refers to gd32f3x0_rcu.o(.text.rcu_lxtal_drive_capability_config) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_flag_get) refers to gd32f3x0_rcu.o(.text.rcu_flag_get) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_all_reset_flag_clear) refers to gd32f3x0_rcu.o(.text.rcu_all_reset_flag_clear) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_interrupt_flag_get) refers to gd32f3x0_rcu.o(.text.rcu_interrupt_flag_get) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_interrupt_flag_clear) refers to gd32f3x0_rcu.o(.text.rcu_interrupt_flag_clear) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_interrupt_enable) refers to gd32f3x0_rcu.o(.text.rcu_interrupt_enable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_interrupt_disable) refers to gd32f3x0_rcu.o(.text.rcu_interrupt_disable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_stab_wait) refers to gd32f3x0_rcu.o(.text.rcu_osci_stab_wait) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_on) refers to gd32f3x0_rcu.o(.text.rcu_osci_on) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_off) refers to gd32f3x0_rcu.o(.text.rcu_osci_off) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_bypass_mode_enable) refers to gd32f3x0_rcu.o(.text.rcu_osci_bypass_mode_enable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_bypass_mode_disable) refers to gd32f3x0_rcu.o(.text.rcu_osci_bypass_mode_disable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_hxtal_clock_monitor_enable) refers to gd32f3x0_rcu.o(.text.rcu_hxtal_clock_monitor_enable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_hxtal_clock_monitor_disable) refers to gd32f3x0_rcu.o(.text.rcu_hxtal_clock_monitor_disable) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_irc8m_adjust_value_set) refers to gd32f3x0_rcu.o(.text.rcu_irc8m_adjust_value_set) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_irc28m_adjust_value_set) refers to gd32f3x0_rcu.o(.text.rcu_irc28m_adjust_value_set) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_voltage_key_unlock) refers to gd32f3x0_rcu.o(.text.rcu_voltage_key_unlock) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_deepsleep_voltage_set) refers to gd32f3x0_rcu.o(.text.rcu_deepsleep_voltage_set) for [Anonymous Symbol]
    gd32f3x0_rcu.o(.text.rcu_clock_freq_get) refers to gd32f3x0_rcu.o(.rodata.cst8) for .L__const.rcu_clock_freq_get.apb2_exp
    gd32f3x0_rcu.o(.ARM.exidx.text.rcu_clock_freq_get) refers to gd32f3x0_rcu.o(.text.rcu_clock_freq_get) for [Anonymous Symbol]
    gd32f3x0_usart.o(.text.usart_deinit) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_usart.o(.text.usart_deinit) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_usart.o(.ARM.exidx.text.usart_deinit) refers to gd32f3x0_usart.o(.text.usart_deinit) for [Anonymous Symbol]
    gd32f3x0_usart.o(.text.usart_baudrate_set) refers to gd32f3x0_rcu.o(.text.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f3x0_usart.o(.ARM.exidx.text.usart_baudrate_set) refers to gd32f3x0_usart.o(.text.usart_baudrate_set) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_parity_config) refers to gd32f3x0_usart.o(.text.usart_parity_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_word_length_set) refers to gd32f3x0_usart.o(.text.usart_word_length_set) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_stop_bit_set) refers to gd32f3x0_usart.o(.text.usart_stop_bit_set) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_enable) refers to gd32f3x0_usart.o(.text.usart_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_disable) refers to gd32f3x0_usart.o(.text.usart_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_transmit_config) refers to gd32f3x0_usart.o(.text.usart_transmit_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_receive_config) refers to gd32f3x0_usart.o(.text.usart_receive_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_data_first_config) refers to gd32f3x0_usart.o(.text.usart_data_first_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_invert_config) refers to gd32f3x0_usart.o(.text.usart_invert_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_overrun_enable) refers to gd32f3x0_usart.o(.text.usart_overrun_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_overrun_disable) refers to gd32f3x0_usart.o(.text.usart_overrun_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_oversample_config) refers to gd32f3x0_usart.o(.text.usart_oversample_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_sample_bit_config) refers to gd32f3x0_usart.o(.text.usart_sample_bit_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_receiver_timeout_enable) refers to gd32f3x0_usart.o(.text.usart_receiver_timeout_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_receiver_timeout_disable) refers to gd32f3x0_usart.o(.text.usart_receiver_timeout_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_receiver_timeout_threshold_config) refers to gd32f3x0_usart.o(.text.usart_receiver_timeout_threshold_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_data_transmit) refers to gd32f3x0_usart.o(.text.usart_data_transmit) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_data_receive) refers to gd32f3x0_usart.o(.text.usart_data_receive) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_command_enable) refers to gd32f3x0_usart.o(.text.usart_command_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_address_config) refers to gd32f3x0_usart.o(.text.usart_address_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_address_detection_mode_config) refers to gd32f3x0_usart.o(.text.usart_address_detection_mode_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_mute_mode_enable) refers to gd32f3x0_usart.o(.text.usart_mute_mode_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_mute_mode_disable) refers to gd32f3x0_usart.o(.text.usart_mute_mode_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_mute_mode_wakeup_config) refers to gd32f3x0_usart.o(.text.usart_mute_mode_wakeup_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_lin_mode_enable) refers to gd32f3x0_usart.o(.text.usart_lin_mode_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_lin_mode_disable) refers to gd32f3x0_usart.o(.text.usart_lin_mode_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_lin_break_detection_length_config) refers to gd32f3x0_usart.o(.text.usart_lin_break_detection_length_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_halfduplex_enable) refers to gd32f3x0_usart.o(.text.usart_halfduplex_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_halfduplex_disable) refers to gd32f3x0_usart.o(.text.usart_halfduplex_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_clock_enable) refers to gd32f3x0_usart.o(.text.usart_clock_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_clock_disable) refers to gd32f3x0_usart.o(.text.usart_clock_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_synchronous_clock_config) refers to gd32f3x0_usart.o(.text.usart_synchronous_clock_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_guard_time_config) refers to gd32f3x0_usart.o(.text.usart_guard_time_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_enable) refers to gd32f3x0_usart.o(.text.usart_smartcard_mode_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_disable) refers to gd32f3x0_usart.o(.text.usart_smartcard_mode_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_nack_enable) refers to gd32f3x0_usart.o(.text.usart_smartcard_mode_nack_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_nack_disable) refers to gd32f3x0_usart.o(.text.usart_smartcard_mode_nack_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_early_nack_enable) refers to gd32f3x0_usart.o(.text.usart_smartcard_mode_early_nack_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_early_nack_disable) refers to gd32f3x0_usart.o(.text.usart_smartcard_mode_early_nack_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_autoretry_config) refers to gd32f3x0_usart.o(.text.usart_smartcard_autoretry_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_block_length_config) refers to gd32f3x0_usart.o(.text.usart_block_length_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_irda_mode_enable) refers to gd32f3x0_usart.o(.text.usart_irda_mode_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_irda_mode_disable) refers to gd32f3x0_usart.o(.text.usart_irda_mode_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_prescaler_config) refers to gd32f3x0_usart.o(.text.usart_prescaler_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_irda_lowpower_config) refers to gd32f3x0_usart.o(.text.usart_irda_lowpower_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_hardware_flow_rts_config) refers to gd32f3x0_usart.o(.text.usart_hardware_flow_rts_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_hardware_flow_cts_config) refers to gd32f3x0_usart.o(.text.usart_hardware_flow_cts_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_rs485_driver_enable) refers to gd32f3x0_usart.o(.text.usart_rs485_driver_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_rs485_driver_disable) refers to gd32f3x0_usart.o(.text.usart_rs485_driver_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_driver_assertime_config) refers to gd32f3x0_usart.o(.text.usart_driver_assertime_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_driver_deassertime_config) refers to gd32f3x0_usart.o(.text.usart_driver_deassertime_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_depolarity_config) refers to gd32f3x0_usart.o(.text.usart_depolarity_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_dma_receive_config) refers to gd32f3x0_usart.o(.text.usart_dma_receive_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_dma_transmit_config) refers to gd32f3x0_usart.o(.text.usart_dma_transmit_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_reception_error_dma_enable) refers to gd32f3x0_usart.o(.text.usart_reception_error_dma_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_reception_error_dma_disable) refers to gd32f3x0_usart.o(.text.usart_reception_error_dma_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_wakeup_enable) refers to gd32f3x0_usart.o(.text.usart_wakeup_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_wakeup_disable) refers to gd32f3x0_usart.o(.text.usart_wakeup_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_wakeup_mode_config) refers to gd32f3x0_usart.o(.text.usart_wakeup_mode_config) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_receive_fifo_enable) refers to gd32f3x0_usart.o(.text.usart_receive_fifo_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_receive_fifo_disable) refers to gd32f3x0_usart.o(.text.usart_receive_fifo_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_receive_fifo_counter_number) refers to gd32f3x0_usart.o(.text.usart_receive_fifo_counter_number) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_flag_get) refers to gd32f3x0_usart.o(.text.usart_flag_get) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_flag_clear) refers to gd32f3x0_usart.o(.text.usart_flag_clear) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_interrupt_enable) refers to gd32f3x0_usart.o(.text.usart_interrupt_enable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_interrupt_disable) refers to gd32f3x0_usart.o(.text.usart_interrupt_disable) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_interrupt_flag_get) refers to gd32f3x0_usart.o(.text.usart_interrupt_flag_get) for [Anonymous Symbol]
    gd32f3x0_usart.o(.ARM.exidx.text.usart_interrupt_flag_clear) refers to gd32f3x0_usart.o(.text.usart_interrupt_flag_clear) for [Anonymous Symbol]
    gd32f3x0_misc.o(.ARM.exidx.text.nvic_priority_group_set) refers to gd32f3x0_misc.o(.text.nvic_priority_group_set) for [Anonymous Symbol]
    gd32f3x0_misc.o(.ARM.exidx.text.nvic_irq_enable) refers to gd32f3x0_misc.o(.text.nvic_irq_enable) for [Anonymous Symbol]
    gd32f3x0_misc.o(.ARM.exidx.text.nvic_irq_disable) refers to gd32f3x0_misc.o(.text.nvic_irq_disable) for [Anonymous Symbol]
    gd32f3x0_misc.o(.ARM.exidx.text.nvic_vector_table_set) refers to gd32f3x0_misc.o(.text.nvic_vector_table_set) for [Anonymous Symbol]
    gd32f3x0_misc.o(.ARM.exidx.text.system_lowpower_set) refers to gd32f3x0_misc.o(.text.system_lowpower_set) for [Anonymous Symbol]
    gd32f3x0_misc.o(.ARM.exidx.text.system_lowpower_reset) refers to gd32f3x0_misc.o(.text.system_lowpower_reset) for [Anonymous Symbol]
    gd32f3x0_misc.o(.ARM.exidx.text.systick_clksource_set) refers to gd32f3x0_misc.o(.text.systick_clksource_set) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_unlock) refers to gd32f3x0_fmc.o(.text.fmc_unlock) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_lock) refers to gd32f3x0_fmc.o(.text.fmc_lock) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_wscnt_set) refers to gd32f3x0_fmc.o(.text.fmc_wscnt_set) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_wait_state_enable) refers to gd32f3x0_fmc.o(.text.fmc_wait_state_enable) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_wait_state_disable) refers to gd32f3x0_fmc.o(.text.fmc_wait_state_disable) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.fmc_page_erase) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_page_erase) refers to gd32f3x0_fmc.o(.text.fmc_page_erase) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.fmc_ready_wait) refers to gd32f3x0_fmc.o(.text.fmc_state_get) for fmc_state_get
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_ready_wait) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.fmc_mass_erase) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_mass_erase) refers to gd32f3x0_fmc.o(.text.fmc_mass_erase) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.fmc_word_program) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_word_program) refers to gd32f3x0_fmc.o(.text.fmc_word_program) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.fmc_halfword_program) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_halfword_program) refers to gd32f3x0_fmc.o(.text.fmc_halfword_program) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.fmc_word_reprogram) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_word_reprogram) refers to gd32f3x0_fmc.o(.text.fmc_word_reprogram) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_unlock) refers to gd32f3x0_fmc.o(.text.ob_unlock) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_lock) refers to gd32f3x0_fmc.o(.text.ob_lock) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_reset) refers to gd32f3x0_fmc.o(.text.ob_reset) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.ob_erase) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_erase) refers to gd32f3x0_fmc.o(.text.ob_erase) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_obstat_plevel_get) refers to gd32f3x0_fmc.o(.text.ob_obstat_plevel_get) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.ob_write_protection_enable) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.text.ob_write_protection_enable) refers to gd32f3x0_fmc.o(.text.ob_parm_get) for ob_parm_get
    gd32f3x0_fmc.o(.text.ob_write_protection_enable) refers to gd32f3x0_fmc.o(.text.ob_value_modify) for ob_value_modify
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_write_protection_enable) refers to gd32f3x0_fmc.o(.text.ob_write_protection_enable) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_parm_get) refers to gd32f3x0_fmc.o(.text.ob_parm_get) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_value_modify) refers to gd32f3x0_fmc.o(.text.ob_value_modify) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.ob_security_protection_config) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.text.ob_security_protection_config) refers to gd32f3x0_fmc.o(.text.ob_parm_get) for ob_parm_get
    gd32f3x0_fmc.o(.text.ob_security_protection_config) refers to gd32f3x0_fmc.o(.text.ob_value_modify) for ob_value_modify
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_security_protection_config) refers to gd32f3x0_fmc.o(.text.ob_security_protection_config) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.ob_user_write) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.text.ob_user_write) refers to gd32f3x0_fmc.o(.text.ob_parm_get) for ob_parm_get
    gd32f3x0_fmc.o(.text.ob_user_write) refers to gd32f3x0_fmc.o(.text.ob_value_modify) for ob_value_modify
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_user_write) refers to gd32f3x0_fmc.o(.text.ob_user_write) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.text.ob_data_program) refers to gd32f3x0_fmc.o(.text.fmc_ready_wait) for fmc_ready_wait
    gd32f3x0_fmc.o(.text.ob_data_program) refers to gd32f3x0_fmc.o(.text.ob_parm_get) for ob_parm_get
    gd32f3x0_fmc.o(.text.ob_data_program) refers to gd32f3x0_fmc.o(.text.ob_value_modify) for ob_value_modify
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_data_program) refers to gd32f3x0_fmc.o(.text.ob_data_program) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_user_get) refers to gd32f3x0_fmc.o(.text.ob_user_get) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_data_get) refers to gd32f3x0_fmc.o(.text.ob_data_get) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.ob_write_protection_get) refers to gd32f3x0_fmc.o(.text.ob_write_protection_get) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_interrupt_enable) refers to gd32f3x0_fmc.o(.text.fmc_interrupt_enable) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_interrupt_disable) refers to gd32f3x0_fmc.o(.text.fmc_interrupt_disable) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_flag_get) refers to gd32f3x0_fmc.o(.text.fmc_flag_get) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_flag_clear) refers to gd32f3x0_fmc.o(.text.fmc_flag_clear) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_interrupt_flag_get) refers to gd32f3x0_fmc.o(.text.fmc_interrupt_flag_get) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_interrupt_flag_clear) refers to gd32f3x0_fmc.o(.text.fmc_interrupt_flag_clear) for [Anonymous Symbol]
    gd32f3x0_fmc.o(.ARM.exidx.text.fmc_state_get) refers to gd32f3x0_fmc.o(.text.fmc_state_get) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_deinit) refers to gd32f3x0_exti.o(.text.exti_deinit) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_init) refers to gd32f3x0_exti.o(.text.exti_init) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_interrupt_enable) refers to gd32f3x0_exti.o(.text.exti_interrupt_enable) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_interrupt_disable) refers to gd32f3x0_exti.o(.text.exti_interrupt_disable) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_event_enable) refers to gd32f3x0_exti.o(.text.exti_event_enable) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_event_disable) refers to gd32f3x0_exti.o(.text.exti_event_disable) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_software_interrupt_enable) refers to gd32f3x0_exti.o(.text.exti_software_interrupt_enable) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_software_interrupt_disable) refers to gd32f3x0_exti.o(.text.exti_software_interrupt_disable) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_flag_get) refers to gd32f3x0_exti.o(.text.exti_flag_get) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_flag_clear) refers to gd32f3x0_exti.o(.text.exti_flag_clear) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_interrupt_flag_get) refers to gd32f3x0_exti.o(.text.exti_interrupt_flag_get) for [Anonymous Symbol]
    gd32f3x0_exti.o(.ARM.exidx.text.exti_interrupt_flag_clear) refers to gd32f3x0_exti.o(.text.exti_interrupt_flag_clear) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.text.syscfg_deinit) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f3x0_syscfg.o(.text.syscfg_deinit) refers to gd32f3x0_rcu.o(.text.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_deinit) refers to gd32f3x0_syscfg.o(.text.syscfg_deinit) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_dma_remap_enable) refers to gd32f3x0_syscfg.o(.text.syscfg_dma_remap_enable) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_dma_remap_disable) refers to gd32f3x0_syscfg.o(.text.syscfg_dma_remap_disable) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_high_current_enable) refers to gd32f3x0_syscfg.o(.text.syscfg_high_current_enable) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_high_current_disable) refers to gd32f3x0_syscfg.o(.text.syscfg_high_current_disable) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_exti_line_config) refers to gd32f3x0_syscfg.o(.text.syscfg_exti_line_config) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_lock_config) refers to gd32f3x0_syscfg.o(.text.syscfg_lock_config) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_flag_get) refers to gd32f3x0_syscfg.o(.text.syscfg_flag_get) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_flag_clear) refers to gd32f3x0_syscfg.o(.text.syscfg_flag_clear) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_compensation_config) refers to gd32f3x0_syscfg.o(.text.syscfg_compensation_config) for [Anonymous Symbol]
    gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_cps_rdy_flag_get) refers to gd32f3x0_syscfg.o(.text.syscfg_cps_rdy_flag_get) for [Anonymous Symbol]
    startup_gd32f3x0.o(RESET) refers to startup_gd32f3x0.o(STACK) for __initial_sp
    startup_gd32f3x0.o(RESET) refers to startup_gd32f3x0.o(.text) for Reset_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.NMI_Handler) for NMI_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.SVC_Handler) for SVC_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_gd32f3x0.o(RESET) refers to gd32f3x0_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_gd32f3x0.o(.text) refers to system_gd32f3x0.o(.text.SystemInit) for SystemInit
    startup_gd32f3x0.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f3x0.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f3x0.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.system_clock_config), (8 bytes).
    Removing main.o(.ARM.exidx.text.board_init), (8 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gd32f3x0_it.o(.text), (0 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing gd32f3x0_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing systick.o(.text), (0 bytes).
    Removing systick.o(.ARM.exidx.text.systick_config), (8 bytes).
    Removing systick.o(.ARM.exidx.text.delay_1ms), (8 bytes).
    Removing systick.o(.ARM.exidx.text.delay_decrement), (8 bytes).
    Removing systick.o(.ARM.exidx.text.systick_get_tick), (8 bytes).
    Removing cm1103_driver.o(.text), (0 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_i2c_scan), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_init), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_write_register), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_read_register), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_config_channel), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_update_continuous_data), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_read_conversion), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_reset_cache), (8 bytes).
    Removing cm1103_driver.o(.text.cm1103_adc_to_voltage), (36 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_adc_to_voltage), (8 bytes).
    Removing cm1103_driver.o(.text.cm1103_get_cache_status), (124 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_get_cache_status), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_is_cached_data_valid), (8 bytes).
    Removing cm1103_driver.o(.ARM.exidx.text.cm1103_read_all_channels), (8 bytes).
    Removing soft_i2c.o(.text), (0 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_init), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_delay), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_start), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_stop), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_send_ack), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_send_nack), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_wait_ack), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_send_byte), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_read_byte), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_write_reg), (8 bytes).
    Removing soft_i2c.o(.ARM.exidx.text.soft_i2c_read_reg), (8 bytes).
    Removing rs485_comm.o(.text), (0 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_init), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_send_data), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_calculate_checksum), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_get_received_command), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_process_received_byte), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_process_command), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_send_heartbeat_response), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_send_quadrant_data_response), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_send_error_response), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_send_system_ready), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_send_quadrant_data_auto), (8 bytes).
    Removing rs485_comm.o(.ARM.exidx.text.rs485_uart_irq_handler), (8 bytes).
    Removing rs485_comm.o(.rodata..L__const.rs485_send_heartbeat_response.response), (6 bytes).
    Removing rs485_comm.o(.rodata..L__const.rs485_send_system_ready.response), (7 bytes).
    Removing system_gd32f3x0.o(.text), (0 bytes).
    Removing system_gd32f3x0.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_gd32f3x0.o(.text.SystemCoreClockUpdate), (168 bytes).
    Removing system_gd32f3x0.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_gd32f3x0.o(.text.gd32f3x0_firmware_version_get), (6 bytes).
    Removing system_gd32f3x0.o(.ARM.exidx.text.gd32f3x0_firmware_version_get), (8 bytes).
    Removing gd32f3x0_adc.o(.text), (0 bytes).
    Removing gd32f3x0_adc.o(.text.adc_deinit), (22 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_deinit), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_enable), (24 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_disable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_disable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_calibration_enable), (36 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_calibration_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_dma_mode_enable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_dma_mode_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_dma_mode_disable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_dma_mode_disable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_tempsensor_vrefint_enable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_tempsensor_vrefint_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_tempsensor_vrefint_disable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_tempsensor_vrefint_disable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_vbat_enable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_vbat_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_vbat_disable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_vbat_disable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_discontinuous_mode_config), (68 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_discontinuous_mode_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_special_function_config), (88 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_special_function_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_data_alignment_config), (24 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_data_alignment_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_channel_length_config), (76 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_channel_length_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_regular_channel_config), (164 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_regular_channel_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_inserted_channel_config), (112 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_inserted_channel_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_inserted_channel_offset_config), (32 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_inserted_channel_offset_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_external_trigger_config), (64 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_external_trigger_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_external_trigger_source_config), (40 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_external_trigger_source_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_software_trigger_enable), (32 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_software_trigger_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_regular_data_read), (12 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_regular_data_read), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_inserted_data_read), (48 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_inserted_data_read), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_watchdog_single_channel_enable), (40 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_watchdog_single_channel_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_watchdog_group_channel_enable), (56 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_watchdog_group_channel_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_watchdog_disable), (20 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_watchdog_disable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_watchdog_threshold_config), (20 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_watchdog_threshold_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_resolution_config), (24 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_resolution_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_oversample_mode_config), (44 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_oversample_mode_config), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_oversample_mode_enable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_oversample_mode_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_oversample_mode_disable), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_oversample_mode_disable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_flag_get), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_flag_get), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_flag_clear), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_flag_clear), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_interrupt_enable), (44 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_interrupt_enable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_interrupt_disable), (44 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_interrupt_disable), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_interrupt_flag_get), (64 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_interrupt_flag_get), (8 bytes).
    Removing gd32f3x0_adc.o(.text.adc_interrupt_flag_clear), (16 bytes).
    Removing gd32f3x0_adc.o(.ARM.exidx.text.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f3x0_gpio.o(.text), (0 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_deinit), (128 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_deinit), (8 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_mode_set), (8 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_output_options_set), (8 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_bit_set), (8 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_bit_reset), (8 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_bit_write), (12 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_bit_write), (8 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_port_write), (4 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_port_write), (8 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_input_bit_get), (8 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_input_port_get), (6 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_input_port_get), (8 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_output_bit_get), (10 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_output_bit_get), (8 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_output_port_get), (6 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_output_port_get), (8 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_af_set), (8 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_pin_lock), (16 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_pin_lock), (8 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_bit_toggle), (4 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_bit_toggle), (8 bytes).
    Removing gd32f3x0_gpio.o(.text.gpio_port_toggle), (8 bytes).
    Removing gd32f3x0_gpio.o(.ARM.exidx.text.gpio_port_toggle), (8 bytes).
    Removing gd32f3x0_rcu.o(.text), (0 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_deinit), (132 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_deinit), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_clock_enable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_periph_clock_disable), (32 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_clock_disable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_periph_clock_sleep_enable), (28 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_clock_sleep_enable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_periph_clock_sleep_disable), (32 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_clock_sleep_disable), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_reset_enable), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_periph_reset_disable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_bkp_reset_enable), (16 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_bkp_reset_enable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_bkp_reset_disable), (16 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_bkp_reset_disable), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_system_clock_source_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_system_clock_source_get), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_ahb_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_apb1_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_apb2_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_adc_clock_config), (148 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_adc_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_usbfs_clock_config), (44 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_usbfs_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_ckout_config), (20 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_ckout_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_pll_preselection_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_pll_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_usart_clock_config), (24 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_usart_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_cec_clock_config), (24 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_cec_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_rtc_clock_config), (24 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_rtc_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_ck48m_clock_config), (20 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_ck48m_clock_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_hxtal_prediv_config), (20 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_hxtal_prediv_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_lxtal_drive_capability_config), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_flag_get), (28 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_flag_get), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_all_reset_flag_clear), (16 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_all_reset_flag_clear), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_interrupt_flag_get), (28 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_interrupt_flag_get), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_interrupt_flag_clear), (28 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_interrupt_flag_clear), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_interrupt_enable), (28 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_interrupt_enable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_interrupt_disable), (32 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_interrupt_disable), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_stab_wait), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_on), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_osci_off), (32 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_off), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_osci_bypass_mode_enable), (56 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_bypass_mode_enable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_osci_bypass_mode_disable), (56 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_osci_bypass_mode_disable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_hxtal_clock_monitor_enable), (16 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_hxtal_clock_monitor_enable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_hxtal_clock_monitor_disable), (16 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_hxtal_clock_monitor_disable), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_irc8m_adjust_value_set), (20 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_irc8m_adjust_value_set), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_irc28m_adjust_value_set), (20 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_irc28m_adjust_value_set), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_voltage_key_unlock), (28 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_voltage_key_unlock), (8 bytes).
    Removing gd32f3x0_rcu.o(.text.rcu_deepsleep_voltage_set), (24 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_deepsleep_voltage_set), (8 bytes).
    Removing gd32f3x0_rcu.o(.ARM.exidx.text.rcu_clock_freq_get), (8 bytes).
    Removing gd32f3x0_usart.o(.text), (0 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_deinit), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_baudrate_set), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_parity_config), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_word_length_set), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_stop_bit_set), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_disable), (10 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_transmit_config), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_receive_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_data_first_config), (24 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_data_first_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_invert_config), (92 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_invert_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_overrun_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_overrun_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_overrun_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_overrun_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_oversample_config), (24 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_oversample_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_sample_bit_config), (24 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_sample_bit_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_receiver_timeout_enable), (10 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_receiver_timeout_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_receiver_timeout_disable), (10 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_receiver_timeout_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_receiver_timeout_threshold_config), (16 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_receiver_timeout_threshold_config), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_data_transmit), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_data_receive), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_command_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_command_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_address_config), (26 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_address_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_address_detection_mode_config), (28 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_address_detection_mode_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_mute_mode_enable), (10 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_mute_mode_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_mute_mode_disable), (10 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_mute_mode_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_mute_mode_wakeup_config), (24 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_mute_mode_wakeup_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_lin_mode_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_lin_mode_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_lin_mode_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_lin_mode_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_lin_break_detection_length_config), (28 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_lin_break_detection_length_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_halfduplex_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_halfduplex_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_halfduplex_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_halfduplex_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_clock_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_clock_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_clock_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_clock_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_synchronous_clock_config), (60 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_synchronous_clock_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_guard_time_config), (28 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_guard_time_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_smartcard_mode_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_smartcard_mode_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_smartcard_mode_nack_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_nack_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_smartcard_mode_nack_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_nack_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_smartcard_mode_early_nack_enable), (14 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_early_nack_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_smartcard_mode_early_nack_disable), (14 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_mode_early_nack_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_smartcard_autoretry_config), (30 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_smartcard_autoretry_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_block_length_config), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_block_length_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_irda_mode_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_irda_mode_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_irda_mode_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_irda_mode_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_prescaler_config), (24 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_prescaler_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_irda_lowpower_config), (28 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_irda_lowpower_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_hardware_flow_rts_config), (24 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_hardware_flow_rts_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_hardware_flow_cts_config), (24 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_hardware_flow_cts_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_rs485_driver_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_rs485_driver_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_rs485_driver_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_rs485_driver_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_driver_assertime_config), (30 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_driver_assertime_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_driver_deassertime_config), (30 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_driver_deassertime_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_depolarity_config), (28 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_depolarity_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_dma_receive_config), (20 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_dma_receive_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_dma_transmit_config), (20 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_dma_transmit_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_reception_error_dma_enable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_reception_error_dma_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_reception_error_dma_disable), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_reception_error_dma_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_wakeup_enable), (10 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_wakeup_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_wakeup_disable), (10 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_wakeup_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_wakeup_mode_config), (28 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_wakeup_mode_config), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_receive_fifo_enable), (14 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_receive_fifo_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_receive_fifo_disable), (14 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_receive_fifo_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_receive_fifo_counter_number), (10 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_receive_fifo_counter_number), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_flag_get), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_flag_clear), (18 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_flag_clear), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_interrupt_enable), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_interrupt_disable), (24 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_interrupt_disable), (8 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_interrupt_flag_get), (8 bytes).
    Removing gd32f3x0_usart.o(.text.usart_interrupt_flag_clear), (44 bytes).
    Removing gd32f3x0_usart.o(.ARM.exidx.text.usart_interrupt_flag_clear), (8 bytes).
    Removing gd32f3x0_misc.o(.text), (0 bytes).
    Removing gd32f3x0_misc.o(.text.nvic_priority_group_set), (20 bytes).
    Removing gd32f3x0_misc.o(.ARM.exidx.text.nvic_priority_group_set), (8 bytes).
    Removing gd32f3x0_misc.o(.ARM.exidx.text.nvic_irq_enable), (8 bytes).
    Removing gd32f3x0_misc.o(.text.nvic_irq_disable), (24 bytes).
    Removing gd32f3x0_misc.o(.ARM.exidx.text.nvic_irq_disable), (8 bytes).
    Removing gd32f3x0_misc.o(.ARM.exidx.text.nvic_vector_table_set), (8 bytes).
    Removing gd32f3x0_misc.o(.text.system_lowpower_set), (16 bytes).
    Removing gd32f3x0_misc.o(.ARM.exidx.text.system_lowpower_set), (8 bytes).
    Removing gd32f3x0_misc.o(.text.system_lowpower_reset), (16 bytes).
    Removing gd32f3x0_misc.o(.ARM.exidx.text.system_lowpower_reset), (8 bytes).
    Removing gd32f3x0_misc.o(.text.systick_clksource_set), (24 bytes).
    Removing gd32f3x0_misc.o(.ARM.exidx.text.systick_clksource_set), (8 bytes).
    Removing gd32f3x0_fmc.o(.text), (0 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_unlock), (32 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_unlock), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_lock), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_lock), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_wscnt_set), (20 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_wscnt_set), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_wait_state_enable), (52 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_wait_state_enable), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_wait_state_disable), (52 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_wait_state_disable), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_page_erase), (64 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_page_erase), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_ready_wait), (30 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_ready_wait), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_mass_erase), (56 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_mass_erase), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_word_program), (52 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_word_program), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_halfword_program), (52 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_halfword_program), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_word_reprogram), (76 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_word_reprogram), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_unlock), (32 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_unlock), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_lock), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_lock), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_reset), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_reset), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_erase), (124 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_erase), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_obstat_plevel_get), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_obstat_plevel_get), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_write_protection_enable), (216 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_write_protection_enable), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_parm_get), (32 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_parm_get), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_value_modify), (112 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_value_modify), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_security_protection_config), (144 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_security_protection_config), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_user_write), (124 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_user_write), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_data_program), (156 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_data_program), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_user_get), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_user_get), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_data_get), (12 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_data_get), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.ob_write_protection_get), (12 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.ob_write_protection_get), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_interrupt_enable), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_interrupt_enable), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_interrupt_disable), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_interrupt_disable), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_flag_get), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_flag_get), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_flag_clear), (12 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_flag_clear), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_interrupt_flag_get), (16 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_interrupt_flag_get), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_interrupt_flag_clear), (8 bytes).
    Removing gd32f3x0_fmc.o(.text.fmc_state_get), (36 bytes).
    Removing gd32f3x0_fmc.o(.ARM.exidx.text.fmc_state_get), (8 bytes).
    Removing gd32f3x0_exti.o(.text), (0 bytes).
    Removing gd32f3x0_exti.o(.text.exti_deinit), (24 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_deinit), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_init), (104 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_init), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_interrupt_enable), (16 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_interrupt_enable), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_interrupt_disable), (16 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_interrupt_disable), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_event_enable), (16 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_event_enable), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_event_disable), (16 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_event_disable), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_software_interrupt_enable), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_software_interrupt_disable), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_flag_get), (16 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_flag_get), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_flag_clear), (12 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_flag_clear), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_interrupt_flag_get), (16 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_interrupt_flag_get), (8 bytes).
    Removing gd32f3x0_exti.o(.text.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f3x0_exti.o(.ARM.exidx.text.exti_interrupt_flag_clear), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text), (0 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_deinit), (22 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_deinit), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_dma_remap_enable), (16 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_dma_remap_enable), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_dma_remap_disable), (16 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_dma_remap_disable), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_high_current_enable), (16 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_high_current_enable), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_high_current_disable), (16 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_high_current_disable), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_exti_line_config), (100 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_exti_line_config), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_lock_config), (16 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_lock_config), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_flag_get), (16 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_flag_get), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_flag_clear), (16 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_flag_clear), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_compensation_config), (20 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_compensation_config), (8 bytes).
    Removing gd32f3x0_syscfg.o(.text.syscfg_cps_rdy_flag_get), (16 bytes).
    Removing gd32f3x0_syscfg.o(.ARM.exidx.text.syscfg_cps_rdy_flag_get), (8 bytes).
    Removing startup_gd32f3x0.o(HEAP), (1024 bytes).

496 unused section(s) (total 9775 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    Drivers\CMSIS\Source\ARM\startup_gd32f3x0.s 0x00000000   Number         0  startup_gd32f3x0.o ABSOLUTE
    cm1103_driver.c                          0x00000000   Number         0  cm1103_driver.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    gd32f3x0_adc.c                           0x00000000   Number         0  gd32f3x0_adc.o ABSOLUTE
    gd32f3x0_exti.c                          0x00000000   Number         0  gd32f3x0_exti.o ABSOLUTE
    gd32f3x0_fmc.c                           0x00000000   Number         0  gd32f3x0_fmc.o ABSOLUTE
    gd32f3x0_gpio.c                          0x00000000   Number         0  gd32f3x0_gpio.o ABSOLUTE
    gd32f3x0_it.c                            0x00000000   Number         0  gd32f3x0_it.o ABSOLUTE
    gd32f3x0_misc.c                          0x00000000   Number         0  gd32f3x0_misc.o ABSOLUTE
    gd32f3x0_rcu.c                           0x00000000   Number         0  gd32f3x0_rcu.o ABSOLUTE
    gd32f3x0_syscfg.c                        0x00000000   Number         0  gd32f3x0_syscfg.o ABSOLUTE
    gd32f3x0_usart.c                         0x00000000   Number         0  gd32f3x0_usart.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    rs485_comm.c                             0x00000000   Number         0  rs485_comm.o ABSOLUTE
    soft_i2c.c                               0x00000000   Number         0  soft_i2c.o ABSOLUTE
    system_gd32f3x0.c                        0x00000000   Number         0  system_gd32f3x0.o ABSOLUTE
    systick.c                                0x00000000   Number         0  systick.o ABSOLUTE
    RESET                                    0x08000000   Section      336  startup_gd32f3x0.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000150   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000150   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000154   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000158   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000158   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000158   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x08000160   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x08000160   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000160   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000160   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x08000164   Number         0  startup_gd32f3x0.o(.text)
    .text                                    0x08000164   Section       36  startup_gd32f3x0.o(.text)
    .text                                    0x08000188   Section        0  memcpya.o(.text)
    .text                                    0x080001ac   Section       36  init.o(.text)
    [Anonymous Symbol]                       0x080001d0   Section        0  gd32f3x0_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x080001d2   Section        0  gd32f3x0_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x080001d4   Section        0  gd32f3x0_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080001d6   Section        0  gd32f3x0_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x080001d8   Section        0  gd32f3x0_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x080001da   Section        0  gd32f3x0_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x080001dc   Section        0  gd32f3x0_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080001de   Section        0  gd32f3x0_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080001e4   Section        0  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_0                             0x08000470   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_1                             0x08000474   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_2                             0x08000478   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_3                             0x0800047c   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_4                             0x08000480   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_5                             0x08000484   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_6                             0x08000488   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_7                             0x0800048c   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_8                             0x08000490   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_9                             0x08000494   Number         4  system_gd32f3x0.o(.text.SystemInit)
    __arm_cp.0_10                            0x08000498   Number         4  system_gd32f3x0.o(.text.SystemInit)
    [Anonymous Symbol]                       0x0800049c   Section        0  gd32f3x0_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x080004a0   Section        0  gd32f3x0_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x080004a2   Section        0  main.o(.text.board_init)
    [Anonymous Symbol]                       0x080004c0   Section        0  cm1103_driver.o(.text.cm1103_config_channel)
    __arm_cp.4_0                             0x08000514   Number         4  cm1103_driver.o(.text.cm1103_config_channel)
    [Anonymous Symbol]                       0x08000518   Section        0  cm1103_driver.o(.text.cm1103_i2c_scan)
    [Anonymous Symbol]                       0x08000548   Section        0  cm1103_driver.o(.text.cm1103_init)
    [Anonymous Symbol]                       0x080005d0   Section        0  cm1103_driver.o(.text.cm1103_is_cached_data_valid)
    [Anonymous Symbol]                       0x080005fc   Section        0  cm1103_driver.o(.text.cm1103_read_all_channels)
    __arm_cp.11_0                            0x08000688   Number         4  cm1103_driver.o(.text.cm1103_read_all_channels)
    [Anonymous Symbol]                       0x08000694   Section        0  cm1103_driver.o(.text.cm1103_read_conversion)
    [Anonymous Symbol]                       0x0800069c   Section        0  cm1103_driver.o(.text.cm1103_read_register)
    [Anonymous Symbol]                       0x080006b0   Section        0  cm1103_driver.o(.text.cm1103_reset_cache)
    [Anonymous Symbol]                       0x080006e0   Section        0  cm1103_driver.o(.text.cm1103_update_continuous_data)
    __arm_cp.5_0                             0x0800079c   Number         4  cm1103_driver.o(.text.cm1103_update_continuous_data)
    __arm_cp.5_1                             0x080007a0   Number         4  cm1103_driver.o(.text.cm1103_update_continuous_data)
    [Anonymous Symbol]                       0x080007a8   Section        0  cm1103_driver.o(.text.cm1103_write_register)
    [Anonymous Symbol]                       0x080007bc   Section        0  systick.o(.text.delay_1ms)
    [Anonymous Symbol]                       0x080007c8   Section        0  systick.o(.text.delay_decrement)
    __arm_cp.2_0                             0x080007dc   Number         4  systick.o(.text.delay_decrement)
    [Anonymous Symbol]                       0x080007e0   Section        0  gd32f3x0_gpio.o(.text.gpio_af_set)
    [Anonymous Symbol]                       0x08000850   Section        0  gd32f3x0_gpio.o(.text.gpio_bit_reset)
    [Anonymous Symbol]                       0x08000854   Section        0  gd32f3x0_gpio.o(.text.gpio_bit_set)
    [Anonymous Symbol]                       0x08000858   Section        0  gd32f3x0_gpio.o(.text.gpio_input_bit_get)
    [Anonymous Symbol]                       0x08000862   Section        0  gd32f3x0_gpio.o(.text.gpio_mode_set)
    [Anonymous Symbol]                       0x080008b4   Section        0  gd32f3x0_gpio.o(.text.gpio_output_options_set)
    [Anonymous Symbol]                       0x0800092c   Section        0  main.o(.text.main)
    __arm_cp.2_0                             0x080009bc   Number         4  main.o(.text.main)
    __arm_cp.2_1                             0x080009c0   Number         4  main.o(.text.main)
    [Anonymous Symbol]                       0x080009c4   Section        0  gd32f3x0_misc.o(.text.nvic_irq_enable)
    __arm_cp.1_0                             0x08000a14   Number         4  gd32f3x0_misc.o(.text.nvic_irq_enable)
    __arm_cp.1_1                             0x08000a18   Number         4  gd32f3x0_misc.o(.text.nvic_irq_enable)
    __arm_cp.1_2                             0x08000a1c   Number         4  gd32f3x0_misc.o(.text.nvic_irq_enable)
    [Anonymous Symbol]                       0x08000a20   Section        0  gd32f3x0_misc.o(.text.nvic_vector_table_set)
    __arm_cp.3_0                             0x08000a30   Number         4  gd32f3x0_misc.o(.text.nvic_vector_table_set)
    __arm_cp.3_1                             0x08000a34   Number         4  gd32f3x0_misc.o(.text.nvic_vector_table_set)
    [Anonymous Symbol]                       0x08000a38   Section        0  gd32f3x0_rcu.o(.text.rcu_ahb_clock_config)
    [Anonymous Symbol]                       0x08000a48   Section        0  gd32f3x0_rcu.o(.text.rcu_apb1_clock_config)
    [Anonymous Symbol]                       0x08000a58   Section        0  gd32f3x0_rcu.o(.text.rcu_apb2_clock_config)
    [Anonymous Symbol]                       0x08000a68   Section        0  gd32f3x0_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.42_0                            0x08000bec   Number         4  gd32f3x0_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.42_1                            0x08000bf0   Number         4  gd32f3x0_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.42_2                            0x08000bf4   Number         4  gd32f3x0_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.42_3                            0x08000bf8   Number         4  gd32f3x0_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.42_4                            0x08000bfc   Number         4  gd32f3x0_rcu.o(.text.rcu_clock_freq_get)
    __arm_cp.42_6                            0x08000c10   Number         4  gd32f3x0_rcu.o(.text.rcu_clock_freq_get)
    [Anonymous Symbol]                       0x08000c14   Section        0  gd32f3x0_rcu.o(.text.rcu_osci_on)
    [Anonymous Symbol]                       0x08000c2c   Section        0  gd32f3x0_rcu.o(.text.rcu_osci_stab_wait)
    __arm_cp.31_0                            0x08000d24   Number         4  gd32f3x0_rcu.o(.text.rcu_osci_stab_wait)
    [Anonymous Symbol]                       0x08000d28   Section        0  gd32f3x0_rcu.o(.text.rcu_periph_clock_enable)
    [Anonymous Symbol]                       0x08000d40   Section        0  gd32f3x0_rcu.o(.text.rcu_periph_reset_disable)
    [Anonymous Symbol]                       0x08000d5c   Section        0  gd32f3x0_rcu.o(.text.rcu_periph_reset_enable)
    __arm_cp.5_0                             0x08000d74   Number         4  gd32f3x0_rcu.o(.text.rcu_periph_reset_enable)
    [Anonymous Symbol]                       0x08000d78   Section        0  gd32f3x0_rcu.o(.text.rcu_pll_config)
    __arm_cp.18_1                            0x08000da4   Number         4  gd32f3x0_rcu.o(.text.rcu_pll_config)
    [Anonymous Symbol]                       0x08000da8   Section        0  gd32f3x0_rcu.o(.text.rcu_pll_preselection_config)
    __arm_cp.17_0                            0x08000dbc   Number         4  gd32f3x0_rcu.o(.text.rcu_pll_preselection_config)
    [Anonymous Symbol]                       0x08000dc0   Section        0  gd32f3x0_rcu.o(.text.rcu_system_clock_source_config)
    [Anonymous Symbol]                       0x08000dd0   Section        0  gd32f3x0_rcu.o(.text.rcu_system_clock_source_get)
    __arm_cp.10_0                            0x08000ddc   Number         4  gd32f3x0_rcu.o(.text.rcu_system_clock_source_get)
    [Anonymous Symbol]                       0x08000de0   Section        0  rs485_comm.o(.text.rs485_calculate_checksum)
    [Anonymous Symbol]                       0x08000df4   Section        0  rs485_comm.o(.text.rs485_get_received_command)
    [Anonymous Symbol]                       0x08000e04   Section        0  rs485_comm.o(.text.rs485_init)
    __arm_cp.0_0                             0x08000ef0   Number         4  rs485_comm.o(.text.rs485_init)
    __arm_cp.0_1                             0x08000ef4   Number         4  rs485_comm.o(.text.rs485_init)
    [Anonymous Symbol]                       0x08000ef8   Section        0  rs485_comm.o(.text.rs485_process_command)
    [Anonymous Symbol]                       0x08000f24   Section        0  rs485_comm.o(.text.rs485_process_received_byte)
    __arm_cp.4_0                             0x08000fa8   Number         4  rs485_comm.o(.text.rs485_process_received_byte)
    [Anonymous Symbol]                       0x08000fac   Section        0  rs485_comm.o(.text.rs485_send_data)
    __arm_cp.1_0                             0x08001038   Number         4  rs485_comm.o(.text.rs485_send_data)
    __arm_cp.1_1                             0x0800103c   Number         4  rs485_comm.o(.text.rs485_send_data)
    __arm_cp.1_2                             0x08001040   Number         4  rs485_comm.o(.text.rs485_send_data)
    __arm_cp.1_4                             0x08001044   Number         4  rs485_comm.o(.text.rs485_send_data)
    [Anonymous Symbol]                       0x08001048   Section        0  rs485_comm.o(.text.rs485_send_error_response)
    [Anonymous Symbol]                       0x08001074   Section        0  rs485_comm.o(.text.rs485_send_heartbeat_response)
    __arm_cp.6_0                             0x08001098   Number         4  rs485_comm.o(.text.rs485_send_heartbeat_response)
    [Anonymous Symbol]                       0x0800109c   Section        0  rs485_comm.o(.text.rs485_send_quadrant_data_auto)
    __arm_cp.10_0                            0x08001130   Number         4  rs485_comm.o(.text.rs485_send_quadrant_data_auto)
    [Anonymous Symbol]                       0x08001134   Section        0  rs485_comm.o(.text.rs485_send_quadrant_data_response)
    __arm_cp.7_0                             0x080011b0   Number         4  rs485_comm.o(.text.rs485_send_quadrant_data_response)
    __arm_cp.7_1                             0x080011b4   Number         4  rs485_comm.o(.text.rs485_send_quadrant_data_response)
    [Anonymous Symbol]                       0x080011b8   Section        0  rs485_comm.o(.text.rs485_send_system_ready)
    __arm_cp.9_0                             0x080011e0   Number         4  rs485_comm.o(.text.rs485_send_system_ready)
    [Anonymous Symbol]                       0x080011e4   Section        0  rs485_comm.o(.text.rs485_uart_irq_handler)
    __arm_cp.11_0                            0x08001204   Number         4  rs485_comm.o(.text.rs485_uart_irq_handler)
    __arm_cp.11_1                            0x08001208   Number         4  rs485_comm.o(.text.rs485_uart_irq_handler)
    [Anonymous Symbol]                       0x0800120c   Section        0  soft_i2c.o(.text.soft_i2c_delay)
    [Anonymous Symbol]                       0x08001226   Section        0  soft_i2c.o(.text.soft_i2c_init)
    [Anonymous Symbol]                       0x08001298   Section        0  soft_i2c.o(.text.soft_i2c_read_byte)
    [Anonymous Symbol]                       0x080012ee   Section        0  soft_i2c.o(.text.soft_i2c_read_reg)
    [Anonymous Symbol]                       0x08001350   Section        0  soft_i2c.o(.text.soft_i2c_send_ack)
    [Anonymous Symbol]                       0x08001390   Section        0  soft_i2c.o(.text.soft_i2c_send_byte)
    [Anonymous Symbol]                       0x080013f4   Section        0  soft_i2c.o(.text.soft_i2c_send_nack)
    [Anonymous Symbol]                       0x08001428   Section        0  soft_i2c.o(.text.soft_i2c_start)
    [Anonymous Symbol]                       0x08001468   Section        0  soft_i2c.o(.text.soft_i2c_stop)
    __arm_cp.3_0                             0x080014a8   Number         4  soft_i2c.o(.text.soft_i2c_stop)
    [Anonymous Symbol]                       0x080014ac   Section        0  soft_i2c.o(.text.soft_i2c_wait_ack)
    [Anonymous Symbol]                       0x0800150c   Section        0  soft_i2c.o(.text.soft_i2c_write_reg)
    [Anonymous Symbol]                       0x08001560   Section        0  main.o(.text.system_clock_config)
    [Anonymous Symbol]                       0x080015b0   Section        0  systick.o(.text.systick_config)
    __arm_cp.0_0                             0x080015dc   Number         4  systick.o(.text.systick_config)
    __arm_cp.0_1                             0x080015e0   Number         4  systick.o(.text.systick_config)
    __arm_cp.0_2                             0x080015e4   Number         4  systick.o(.text.systick_config)
    [Anonymous Symbol]                       0x080015e8   Section        0  systick.o(.text.systick_get_tick)
    __arm_cp.3_0                             0x080015f0   Number         4  systick.o(.text.systick_get_tick)
    [Anonymous Symbol]                       0x080015f4   Section        0  gd32f3x0_usart.o(.text.usart_baudrate_set)
    [Anonymous Symbol]                       0x08001640   Section        0  gd32f3x0_usart.o(.text.usart_data_receive)
    [Anonymous Symbol]                       0x08001648   Section        0  gd32f3x0_usart.o(.text.usart_data_transmit)
    [Anonymous Symbol]                       0x08001650   Section        0  gd32f3x0_usart.o(.text.usart_deinit)
    __arm_cp.0_0                             0x08001688   Number         4  gd32f3x0_usart.o(.text.usart_deinit)
    __arm_cp.0_1                             0x0800168c   Number         4  gd32f3x0_usart.o(.text.usart_deinit)
    [Anonymous Symbol]                       0x08001690   Section        0  gd32f3x0_usart.o(.text.usart_enable)
    [Anonymous Symbol]                       0x0800169a   Section        0  gd32f3x0_usart.o(.text.usart_flag_get)
    [Anonymous Symbol]                       0x080016b0   Section        0  gd32f3x0_usart.o(.text.usart_interrupt_enable)
    [Anonymous Symbol]                       0x080016c8   Section        0  gd32f3x0_usart.o(.text.usart_interrupt_flag_get)
    [Anonymous Symbol]                       0x080016f8   Section        0  gd32f3x0_usart.o(.text.usart_parity_config)
    [Anonymous Symbol]                       0x08001710   Section        0  gd32f3x0_usart.o(.text.usart_receive_config)
    [Anonymous Symbol]                       0x08001720   Section        0  gd32f3x0_usart.o(.text.usart_stop_bit_set)
    [Anonymous Symbol]                       0x08001738   Section        0  gd32f3x0_usart.o(.text.usart_transmit_config)
    [Anonymous Symbol]                       0x08001748   Section        0  gd32f3x0_usart.o(.text.usart_word_length_set)
    i.__scatterload_copy                     0x08001760   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800176e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08001770   Section       14  handlers.o(i.__scatterload_zeroinit)
    [Anonymous Symbol]                       0x08001780   Section        0  rs485_comm.o(.rodata..L__const.rs485_send_quadrant_data_auto.response)
    [Anonymous Symbol]                       0x08001798   Section        0  rs485_comm.o(.rodata..L__const.rs485_send_quadrant_data_response.response)
    .L__const.rcu_clock_freq_get.apb2_exp    0x080017a5   Data           8  gd32f3x0_rcu.o(.rodata.cst8)
    cm1103_update_continuous_data.last_data  0x20000004   Data           8  cm1103_driver.o(.data.cm1103_update_continuous_data.last_data)
    [Anonymous Symbol]                       0x20000004   Section        0  cm1103_driver.o(.data.cm1103_update_continuous_data.last_data)
    current_config                           0x2000000c   Data           2  cm1103_driver.o(.data.current_config)
    [Anonymous Symbol]                       0x2000000c   Section        0  cm1103_driver.o(.data.current_config)
    delay                                    0x20000010   Data           4  systick.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000010   Section        0  systick.o(.bss..L_MergedGlobals)
    systick_counter                          0x20000014   Data           4  systick.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000018   Section        0  cm1103_driver.o(.bss..L_MergedGlobals)
    cm1103_update_continuous_data.current_channel 0x2000001b   Data           1  cm1103_driver.o(.bss..L_MergedGlobals)
    cm1103_update_continuous_data.next_channel 0x2000001c   Data           1  cm1103_driver.o(.bss..L_MergedGlobals)
    cm1103_update_continuous_data.initialized 0x2000001d   Data           1  cm1103_driver.o(.bss..L_MergedGlobals)
    cm1103_update_continuous_data.last_channel_switch 0x20000020   Data           4  cm1103_driver.o(.bss..L_MergedGlobals)
    cm1103_update_continuous_data.consecutive_errors 0x20000024   Data           4  cm1103_driver.o(.bss..L_MergedGlobals)
    cm1103_update_continuous_data.same_data_count 0x20000028   Data           4  cm1103_driver.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000044   Section        0  rs485_comm.o(.bss..L_MergedGlobals)
    rx_index                                 0x20000046   Data           2  rs485_comm.o(.bss..L_MergedGlobals)
    last_rx_time                             0x20000048   Data           4  rs485_comm.o(.bss..L_MergedGlobals)
    rx_buffer                                0x2000004c   Data          32  rs485_comm.o(.bss..L_MergedGlobals)
    tx_buffer                                0x2000006c   Data          32  rs485_comm.o(.bss.tx_buffer)
    [Anonymous Symbol]                       0x2000006c   Section        0  rs485_comm.o(.bss.tx_buffer)
    STACK                                    0x20000090   Section     1024  startup_gd32f3x0.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000150   Number         0  startup_gd32f3x0.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f3x0.o(RESET)
    __Vectors_End                            0x08000150   Data           0  startup_gd32f3x0.o(RESET)
    __main                                   0x08000151   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000151   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000155   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000159   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000159   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000159   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000159   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000161   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000161   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000165   Thumb Code     8  startup_gd32f3x0.o(.text)
    ADC_CMP_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    CEC_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    DMA_Channel0_IRQHandler                  0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    DMA_Channel1_2_IRQHandler                0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    DMA_Channel3_4_IRQHandler                0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    DMA_Channel5_6_IRQHandler                0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    EXTI0_1_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    EXTI2_3_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    EXTI4_15_IRQHandler                      0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    FMC_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    I2C0_ER_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    I2C0_EV_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    I2C1_ER_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    I2C1_EV_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    LVD_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    RCU_CTC_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    RTC_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    SPI0_IRQHandler                          0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    SPI1_IRQHandler                          0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER0_BRK_UP_TRG_COM_IRQHandler         0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER0_Channel_IRQHandler                0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER13_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER14_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER15_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER16_IRQHandler                       0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER1_IRQHandler                        0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER2_IRQHandler                        0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TIMER5_DAC_IRQHandler                    0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    TSI_IRQHandler                           0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    USART0_IRQHandler                        0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    USBFS_IRQHandler                         0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    USBFS_WKUP_IRQHandler                    0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    WWDGT_IRQHandler                         0x0800017f   Thumb Code     0  startup_gd32f3x0.o(.text)
    __aeabi_memcpy                           0x08000189   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000189   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000189   Thumb Code     0  memcpya.o(.text)
    __scatterload                            0x080001ad   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x080001ad   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x080001d1   Thumb Code     2  gd32f3x0_it.o(.text.BusFault_Handler)
    DebugMon_Handler                         0x080001d3   Thumb Code     2  gd32f3x0_it.o(.text.DebugMon_Handler)
    HardFault_Handler                        0x080001d5   Thumb Code     2  gd32f3x0_it.o(.text.HardFault_Handler)
    MemManage_Handler                        0x080001d7   Thumb Code     2  gd32f3x0_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x080001d9   Thumb Code     2  gd32f3x0_it.o(.text.NMI_Handler)
    PendSV_Handler                           0x080001db   Thumb Code     2  gd32f3x0_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x080001dd   Thumb Code     2  gd32f3x0_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x080001df   Thumb Code     4  gd32f3x0_it.o(.text.SysTick_Handler)
    SystemInit                               0x080001e5   Thumb Code   696  system_gd32f3x0.o(.text.SystemInit)
    USART1_IRQHandler                        0x0800049d   Thumb Code     4  gd32f3x0_it.o(.text.USART1_IRQHandler)
    UsageFault_Handler                       0x080004a1   Thumb Code     2  gd32f3x0_it.o(.text.UsageFault_Handler)
    board_init                               0x080004a3   Thumb Code    28  main.o(.text.board_init)
    cm1103_config_channel                    0x080004c1   Thumb Code    88  cm1103_driver.o(.text.cm1103_config_channel)
    cm1103_i2c_scan                          0x08000519   Thumb Code    48  cm1103_driver.o(.text.cm1103_i2c_scan)
    cm1103_init                              0x08000549   Thumb Code   140  cm1103_driver.o(.text.cm1103_init)
    cm1103_is_cached_data_valid              0x080005d1   Thumb Code    48  cm1103_driver.o(.text.cm1103_is_cached_data_valid)
    cm1103_read_all_channels                 0x080005fd   Thumb Code   152  cm1103_driver.o(.text.cm1103_read_all_channels)
    cm1103_read_conversion                   0x08000695   Thumb Code     8  cm1103_driver.o(.text.cm1103_read_conversion)
    cm1103_read_register                     0x0800069d   Thumb Code    20  cm1103_driver.o(.text.cm1103_read_register)
    cm1103_reset_cache                       0x080006b1   Thumb Code    52  cm1103_driver.o(.text.cm1103_reset_cache)
    cm1103_update_continuous_data            0x080006e1   Thumb Code   200  cm1103_driver.o(.text.cm1103_update_continuous_data)
    cm1103_write_register                    0x080007a9   Thumb Code    20  cm1103_driver.o(.text.cm1103_write_register)
    delay_1ms                                0x080007bd   Thumb Code    16  systick.o(.text.delay_1ms)
    delay_decrement                          0x080007c9   Thumb Code    24  systick.o(.text.delay_decrement)
    gpio_af_set                              0x080007e1   Thumb Code   112  gd32f3x0_gpio.o(.text.gpio_af_set)
    gpio_bit_reset                           0x08000851   Thumb Code     4  gd32f3x0_gpio.o(.text.gpio_bit_reset)
    gpio_bit_set                             0x08000855   Thumb Code     4  gd32f3x0_gpio.o(.text.gpio_bit_set)
    gpio_input_bit_get                       0x08000859   Thumb Code    10  gd32f3x0_gpio.o(.text.gpio_input_bit_get)
    gpio_mode_set                            0x08000863   Thumb Code    82  gd32f3x0_gpio.o(.text.gpio_mode_set)
    gpio_output_options_set                  0x080008b5   Thumb Code   120  gd32f3x0_gpio.o(.text.gpio_output_options_set)
    main                                     0x0800092d   Thumb Code   152  main.o(.text.main)
    nvic_irq_enable                          0x080009c5   Thumb Code    92  gd32f3x0_misc.o(.text.nvic_irq_enable)
    nvic_vector_table_set                    0x08000a21   Thumb Code    24  gd32f3x0_misc.o(.text.nvic_vector_table_set)
    rcu_ahb_clock_config                     0x08000a39   Thumb Code    20  gd32f3x0_rcu.o(.text.rcu_ahb_clock_config)
    rcu_apb1_clock_config                    0x08000a49   Thumb Code    20  gd32f3x0_rcu.o(.text.rcu_apb1_clock_config)
    rcu_apb2_clock_config                    0x08000a59   Thumb Code    20  gd32f3x0_rcu.o(.text.rcu_apb2_clock_config)
    rcu_clock_freq_get                       0x08000a69   Thumb Code   428  gd32f3x0_rcu.o(.text.rcu_clock_freq_get)
    rcu_osci_on                              0x08000c15   Thumb Code    28  gd32f3x0_rcu.o(.text.rcu_osci_on)
    rcu_osci_stab_wait                       0x08000c2d   Thumb Code   256  gd32f3x0_rcu.o(.text.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x08000d29   Thumb Code    28  gd32f3x0_rcu.o(.text.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08000d41   Thumb Code    32  gd32f3x0_rcu.o(.text.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08000d5d   Thumb Code    28  gd32f3x0_rcu.o(.text.rcu_periph_reset_enable)
    rcu_pll_config                           0x08000d79   Thumb Code    52  gd32f3x0_rcu.o(.text.rcu_pll_config)
    rcu_pll_preselection_config              0x08000da9   Thumb Code    24  gd32f3x0_rcu.o(.text.rcu_pll_preselection_config)
    rcu_system_clock_source_config           0x08000dc1   Thumb Code    20  gd32f3x0_rcu.o(.text.rcu_system_clock_source_config)
    rcu_system_clock_source_get              0x08000dd1   Thumb Code    16  gd32f3x0_rcu.o(.text.rcu_system_clock_source_get)
    rs485_calculate_checksum                 0x08000de1   Thumb Code    20  rs485_comm.o(.text.rs485_calculate_checksum)
    rs485_get_received_command               0x08000df5   Thumb Code    20  rs485_comm.o(.text.rs485_get_received_command)
    rs485_init                               0x08000e05   Thumb Code   260  rs485_comm.o(.text.rs485_init)
    rs485_process_command                    0x08000ef9   Thumb Code    42  rs485_comm.o(.text.rs485_process_command)
    rs485_process_received_byte              0x08000f25   Thumb Code   136  rs485_comm.o(.text.rs485_process_received_byte)
    rs485_send_data                          0x08000fad   Thumb Code   160  rs485_comm.o(.text.rs485_send_data)
    rs485_send_error_response                0x08001049   Thumb Code    48  rs485_comm.o(.text.rs485_send_error_response)
    rs485_send_heartbeat_response            0x08001075   Thumb Code    40  rs485_comm.o(.text.rs485_send_heartbeat_response)
    rs485_send_quadrant_data_auto            0x0800109d   Thumb Code   152  rs485_comm.o(.text.rs485_send_quadrant_data_auto)
    rs485_send_quadrant_data_response        0x08001135   Thumb Code   132  rs485_comm.o(.text.rs485_send_quadrant_data_response)
    rs485_send_system_ready                  0x080011b9   Thumb Code    44  rs485_comm.o(.text.rs485_send_system_ready)
    rs485_uart_irq_handler                   0x080011e5   Thumb Code    40  rs485_comm.o(.text.rs485_uart_irq_handler)
    soft_i2c_delay                           0x0800120d   Thumb Code    26  soft_i2c.o(.text.soft_i2c_delay)
    soft_i2c_init                            0x08001227   Thumb Code   114  soft_i2c.o(.text.soft_i2c_init)
    soft_i2c_read_byte                       0x08001299   Thumb Code    86  soft_i2c.o(.text.soft_i2c_read_byte)
    soft_i2c_read_reg                        0x080012ef   Thumb Code    98  soft_i2c.o(.text.soft_i2c_read_reg)
    soft_i2c_send_ack                        0x08001351   Thumb Code    68  soft_i2c.o(.text.soft_i2c_send_ack)
    soft_i2c_send_byte                       0x08001391   Thumb Code    98  soft_i2c.o(.text.soft_i2c_send_byte)
    soft_i2c_send_nack                       0x080013f5   Thumb Code    56  soft_i2c.o(.text.soft_i2c_send_nack)
    soft_i2c_start                           0x08001429   Thumb Code    68  soft_i2c.o(.text.soft_i2c_start)
    soft_i2c_stop                            0x08001469   Thumb Code    68  soft_i2c.o(.text.soft_i2c_stop)
    soft_i2c_wait_ack                        0x080014ad   Thumb Code    96  soft_i2c.o(.text.soft_i2c_wait_ack)
    soft_i2c_write_reg                       0x0800150d   Thumb Code    84  soft_i2c.o(.text.soft_i2c_write_reg)
    system_clock_config                      0x08001561   Thumb Code    80  main.o(.text.system_clock_config)
    systick_config                           0x080015b1   Thumb Code    56  systick.o(.text.systick_config)
    systick_get_tick                         0x080015e9   Thumb Code    12  systick.o(.text.systick_get_tick)
    usart_baudrate_set                       0x080015f5   Thumb Code    84  gd32f3x0_usart.o(.text.usart_baudrate_set)
    usart_data_receive                       0x08001641   Thumb Code     8  gd32f3x0_usart.o(.text.usart_data_receive)
    usart_data_transmit                      0x08001649   Thumb Code     8  gd32f3x0_usart.o(.text.usart_data_transmit)
    usart_deinit                             0x08001651   Thumb Code    64  gd32f3x0_usart.o(.text.usart_deinit)
    usart_enable                             0x08001691   Thumb Code    10  gd32f3x0_usart.o(.text.usart_enable)
    usart_flag_get                           0x0800169b   Thumb Code    22  gd32f3x0_usart.o(.text.usart_flag_get)
    usart_interrupt_enable                   0x080016b1   Thumb Code    24  gd32f3x0_usart.o(.text.usart_interrupt_enable)
    usart_interrupt_flag_get                 0x080016c9   Thumb Code    48  gd32f3x0_usart.o(.text.usart_interrupt_flag_get)
    usart_parity_config                      0x080016f9   Thumb Code    24  gd32f3x0_usart.o(.text.usart_parity_config)
    usart_receive_config                     0x08001711   Thumb Code    16  gd32f3x0_usart.o(.text.usart_receive_config)
    usart_stop_bit_set                       0x08001721   Thumb Code    24  gd32f3x0_usart.o(.text.usart_stop_bit_set)
    usart_transmit_config                    0x08001739   Thumb Code    16  gd32f3x0_usart.o(.text.usart_transmit_config)
    usart_word_length_set                    0x08001749   Thumb Code    24  gd32f3x0_usart.o(.text.usart_word_length_set)
    __scatterload_copy                       0x08001761   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800176f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08001771   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x080017b0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080017d0   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_gd32f3x0.o(.data.SystemCoreClock)
    g_rs485_tx_complete                      0x2000000e   Data           1  rs485_comm.o(.data.g_rs485_tx_complete)
    g_cm1103_data_ready                      0x20000018   Data           1  cm1103_driver.o(.bss..L_MergedGlobals)
    g_cm1103_conversion_done                 0x20000019   Data           1  cm1103_driver.o(.bss..L_MergedGlobals)
    g_current_channel_index                  0x2000001a   Data           1  cm1103_driver.o(.bss..L_MergedGlobals)
    g_cached_channel_data                    0x2000002c   Data           8  cm1103_driver.o(.bss..L_MergedGlobals)
    g_last_update_time                       0x20000034   Data          16  cm1103_driver.o(.bss..L_MergedGlobals)
    g_rs485_rx_state                         0x20000044   Data           1  rs485_comm.o(.bss..L_MergedGlobals)
    g_rs485_frame_received                   0x20000045   Data           1  rs485_comm.o(.bss..L_MergedGlobals)
    __initial_sp                             0x20000490   Data           0  startup_gd32f3x0.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000151

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000017e0, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000017d0, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000150   Data   RO          713    RESET               startup_gd32f3x0.o
    0x08000150   0x08000150   0x00000000   Code   RO          720  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000150   0x08000150   0x00000004   Code   RO          727    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000154   0x08000154   0x00000004   Code   RO          730    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000158   0x08000158   0x00000000   Code   RO          732    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000158   0x08000158   0x00000000   Code   RO          734    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000158   0x08000158   0x00000008   Code   RO          735    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000160   0x08000160   0x00000000   Code   RO          737    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000160   0x08000160   0x00000000   Code   RO          739    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000160   0x08000160   0x00000004   Code   RO          728    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000164   0x08000164   0x00000024   Code   RO          714    .text               startup_gd32f3x0.o
    0x08000188   0x08000188   0x00000024   Code   RO          723    .text               mc_w.l(memcpya.o)
    0x080001ac   0x080001ac   0x00000024   Code   RO          741    .text               mc_w.l(init.o)
    0x080001d0   0x080001d0   0x00000002   Code   RO           24    .text.BusFault_Handler  gd32f3x0_it.o
    0x080001d2   0x080001d2   0x00000002   Code   RO           30    .text.DebugMon_Handler  gd32f3x0_it.o
    0x080001d4   0x080001d4   0x00000002   Code   RO           20    .text.HardFault_Handler  gd32f3x0_it.o
    0x080001d6   0x080001d6   0x00000002   Code   RO           22    .text.MemManage_Handler  gd32f3x0_it.o
    0x080001d8   0x080001d8   0x00000002   Code   RO           18    .text.NMI_Handler   gd32f3x0_it.o
    0x080001da   0x080001da   0x00000002   Code   RO           32    .text.PendSV_Handler  gd32f3x0_it.o
    0x080001dc   0x080001dc   0x00000002   Code   RO           28    .text.SVC_Handler   gd32f3x0_it.o
    0x080001de   0x080001de   0x00000004   Code   RO           34    .text.SysTick_Handler  gd32f3x0_it.o
    0x080001e2   0x080001e2   0x00000002   PAD
    0x080001e4   0x080001e4   0x000002b8   Code   RO          171    .text.SystemInit    system_gd32f3x0.o
    0x0800049c   0x0800049c   0x00000004   Code   RO           36    .text.USART1_IRQHandler  gd32f3x0_it.o
    0x080004a0   0x080004a0   0x00000002   Code   RO           26    .text.UsageFault_Handler  gd32f3x0_it.o
    0x080004a2   0x080004a2   0x0000001c   Code   RO            4    .text.board_init    main.o
    0x080004be   0x080004be   0x00000002   PAD
    0x080004c0   0x080004c0   0x00000058   Code   RO           72    .text.cm1103_config_channel  cm1103_driver.o
    0x08000518   0x08000518   0x00000030   Code   RO           64    .text.cm1103_i2c_scan  cm1103_driver.o
    0x08000548   0x08000548   0x00000088   Code   RO           66    .text.cm1103_init   cm1103_driver.o
    0x080005d0   0x080005d0   0x0000002c   Code   RO           84    .text.cm1103_is_cached_data_valid  cm1103_driver.o
    0x080005fc   0x080005fc   0x00000098   Code   RO           86    .text.cm1103_read_all_channels  cm1103_driver.o
    0x08000694   0x08000694   0x00000008   Code   RO           76    .text.cm1103_read_conversion  cm1103_driver.o
    0x0800069c   0x0800069c   0x00000014   Code   RO           70    .text.cm1103_read_register  cm1103_driver.o
    0x080006b0   0x080006b0   0x00000030   Code   RO           78    .text.cm1103_reset_cache  cm1103_driver.o
    0x080006e0   0x080006e0   0x000000c8   Code   RO           74    .text.cm1103_update_continuous_data  cm1103_driver.o
    0x080007a8   0x080007a8   0x00000014   Code   RO           68    .text.cm1103_write_register  cm1103_driver.o
    0x080007bc   0x080007bc   0x0000000c   Code   RO           48    .text.delay_1ms     systick.o
    0x080007c8   0x080007c8   0x00000018   Code   RO           50    .text.delay_decrement  systick.o
    0x080007e0   0x080007e0   0x00000070   Code   RO          290    .text.gpio_af_set   gd32f3x0_gpio.o
    0x08000850   0x08000850   0x00000004   Code   RO          276    .text.gpio_bit_reset  gd32f3x0_gpio.o
    0x08000854   0x08000854   0x00000004   Code   RO          274    .text.gpio_bit_set  gd32f3x0_gpio.o
    0x08000858   0x08000858   0x0000000a   Code   RO          282    .text.gpio_input_bit_get  gd32f3x0_gpio.o
    0x08000862   0x08000862   0x00000052   Code   RO          270    .text.gpio_mode_set  gd32f3x0_gpio.o
    0x080008b4   0x080008b4   0x00000078   Code   RO          272    .text.gpio_output_options_set  gd32f3x0_gpio.o
    0x0800092c   0x0800092c   0x00000098   Code   RO            6    .text.main          main.o
    0x080009c4   0x080009c4   0x0000005c   Code   RO          554    .text.nvic_irq_enable  gd32f3x0_misc.o
    0x08000a20   0x08000a20   0x00000018   Code   RO          558    .text.nvic_vector_table_set  gd32f3x0_misc.o
    0x08000a38   0x08000a38   0x00000010   Code   RO          329    .text.rcu_ahb_clock_config  gd32f3x0_rcu.o
    0x08000a48   0x08000a48   0x00000010   Code   RO          331    .text.rcu_apb1_clock_config  gd32f3x0_rcu.o
    0x08000a58   0x08000a58   0x00000010   Code   RO          333    .text.rcu_apb2_clock_config  gd32f3x0_rcu.o
    0x08000a68   0x08000a68   0x000001ac   Code   RO          391    .text.rcu_clock_freq_get  gd32f3x0_rcu.o
    0x08000c14   0x08000c14   0x00000018   Code   RO          371    .text.rcu_osci_on   gd32f3x0_rcu.o
    0x08000c2c   0x08000c2c   0x000000fc   Code   RO          369    .text.rcu_osci_stab_wait  gd32f3x0_rcu.o
    0x08000d28   0x08000d28   0x00000018   Code   RO          309    .text.rcu_periph_clock_enable  gd32f3x0_rcu.o
    0x08000d40   0x08000d40   0x0000001c   Code   RO          319    .text.rcu_periph_reset_disable  gd32f3x0_rcu.o
    0x08000d5c   0x08000d5c   0x0000001c   Code   RO          317    .text.rcu_periph_reset_enable  gd32f3x0_rcu.o
    0x08000d78   0x08000d78   0x00000030   Code   RO          343    .text.rcu_pll_config  gd32f3x0_rcu.o
    0x08000da8   0x08000da8   0x00000018   Code   RO          341    .text.rcu_pll_preselection_config  gd32f3x0_rcu.o
    0x08000dc0   0x08000dc0   0x00000010   Code   RO          325    .text.rcu_system_clock_source_config  gd32f3x0_rcu.o
    0x08000dd0   0x08000dd0   0x00000010   Code   RO          327    .text.rcu_system_clock_source_get  gd32f3x0_rcu.o
    0x08000de0   0x08000de0   0x00000014   Code   RO          135    .text.rs485_calculate_checksum  rs485_comm.o
    0x08000df4   0x08000df4   0x00000010   Code   RO          137    .text.rs485_get_received_command  rs485_comm.o
    0x08000e04   0x08000e04   0x000000f4   Code   RO          131    .text.rs485_init    rs485_comm.o
    0x08000ef8   0x08000ef8   0x0000002a   Code   RO          141    .text.rs485_process_command  rs485_comm.o
    0x08000f22   0x08000f22   0x00000002   PAD
    0x08000f24   0x08000f24   0x00000088   Code   RO          139    .text.rs485_process_received_byte  rs485_comm.o
    0x08000fac   0x08000fac   0x0000009c   Code   RO          133    .text.rs485_send_data  rs485_comm.o
    0x08001048   0x08001048   0x0000002c   Code   RO          147    .text.rs485_send_error_response  rs485_comm.o
    0x08001074   0x08001074   0x00000028   Code   RO          143    .text.rs485_send_heartbeat_response  rs485_comm.o
    0x0800109c   0x0800109c   0x00000098   Code   RO          151    .text.rs485_send_quadrant_data_auto  rs485_comm.o
    0x08001134   0x08001134   0x00000084   Code   RO          145    .text.rs485_send_quadrant_data_response  rs485_comm.o
    0x080011b8   0x080011b8   0x0000002c   Code   RO          149    .text.rs485_send_system_ready  rs485_comm.o
    0x080011e4   0x080011e4   0x00000028   Code   RO          153    .text.rs485_uart_irq_handler  rs485_comm.o
    0x0800120c   0x0800120c   0x0000001a   Code   RO          102    .text.soft_i2c_delay  soft_i2c.o
    0x08001226   0x08001226   0x00000072   Code   RO          100    .text.soft_i2c_init  soft_i2c.o
    0x08001298   0x08001298   0x00000056   Code   RO          116    .text.soft_i2c_read_byte  soft_i2c.o
    0x080012ee   0x080012ee   0x00000062   Code   RO          120    .text.soft_i2c_read_reg  soft_i2c.o
    0x08001350   0x08001350   0x00000040   Code   RO          108    .text.soft_i2c_send_ack  soft_i2c.o
    0x08001390   0x08001390   0x00000062   Code   RO          114    .text.soft_i2c_send_byte  soft_i2c.o
    0x080013f2   0x080013f2   0x00000002   PAD
    0x080013f4   0x080013f4   0x00000034   Code   RO          110    .text.soft_i2c_send_nack  soft_i2c.o
    0x08001428   0x08001428   0x00000040   Code   RO          104    .text.soft_i2c_start  soft_i2c.o
    0x08001468   0x08001468   0x00000044   Code   RO          106    .text.soft_i2c_stop  soft_i2c.o
    0x080014ac   0x080014ac   0x00000060   Code   RO          112    .text.soft_i2c_wait_ack  soft_i2c.o
    0x0800150c   0x0800150c   0x00000054   Code   RO          118    .text.soft_i2c_write_reg  soft_i2c.o
    0x08001560   0x08001560   0x00000050   Code   RO            2    .text.system_clock_config  main.o
    0x080015b0   0x080015b0   0x00000038   Code   RO           46    .text.systick_config  systick.o
    0x080015e8   0x080015e8   0x0000000c   Code   RO           52    .text.systick_get_tick  systick.o
    0x080015f4   0x080015f4   0x0000004c   Code   RO          405    .text.usart_baudrate_set  gd32f3x0_usart.o
    0x08001640   0x08001640   0x00000008   Code   RO          441    .text.usart_data_receive  gd32f3x0_usart.o
    0x08001648   0x08001648   0x00000008   Code   RO          439    .text.usart_data_transmit  gd32f3x0_usart.o
    0x08001650   0x08001650   0x00000040   Code   RO          403    .text.usart_deinit  gd32f3x0_usart.o
    0x08001690   0x08001690   0x0000000a   Code   RO          413    .text.usart_enable  gd32f3x0_usart.o
    0x0800169a   0x0800169a   0x00000016   Code   RO          531    .text.usart_flag_get  gd32f3x0_usart.o
    0x080016b0   0x080016b0   0x00000018   Code   RO          535    .text.usart_interrupt_enable  gd32f3x0_usart.o
    0x080016c8   0x080016c8   0x00000030   Code   RO          539    .text.usart_interrupt_flag_get  gd32f3x0_usart.o
    0x080016f8   0x080016f8   0x00000018   Code   RO          407    .text.usart_parity_config  gd32f3x0_usart.o
    0x08001710   0x08001710   0x00000010   Code   RO          419    .text.usart_receive_config  gd32f3x0_usart.o
    0x08001720   0x08001720   0x00000018   Code   RO          411    .text.usart_stop_bit_set  gd32f3x0_usart.o
    0x08001738   0x08001738   0x00000010   Code   RO          417    .text.usart_transmit_config  gd32f3x0_usart.o
    0x08001748   0x08001748   0x00000018   Code   RO          409    .text.usart_word_length_set  gd32f3x0_usart.o
    0x08001760   0x08001760   0x0000000e   Code   RO          745    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800176e   0x0800176e   0x00000002   Code   RO          746    i.__scatterload_null  mc_w.l(handlers.o)
    0x08001770   0x08001770   0x0000000e   Code   RO          747    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800177e   0x0800177e   0x00000002   PAD
    0x08001780   0x08001780   0x00000015   Data   RO          160    .rodata..L__const.rs485_send_quadrant_data_auto.response  rs485_comm.o
    0x08001795   0x08001795   0x00000003   PAD
    0x08001798   0x08001798   0x0000000d   Data   RO          158    .rodata..L__const.rs485_send_quadrant_data_response.response  rs485_comm.o
    0x080017a5   0x080017a5   0x00000008   Data   RO          393    .rodata.cst8        gd32f3x0_rcu.o
    0x080017ad   0x080017ad   0x00000003   PAD
    0x080017b0   0x080017b0   0x00000020   Data   RO          744    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080017d0, Size: 0x00000490, Max: 0x00002000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080017d0   0x00000004   Data   RW          177    .data.SystemCoreClock  system_gd32f3x0.o
    0x20000004   0x080017d4   0x00000008   Data   RW           88    .data.cm1103_update_continuous_data.last_data  cm1103_driver.o
    0x2000000c   0x080017dc   0x00000002   Data   RW           89    .data.current_config  cm1103_driver.o
    0x2000000e   0x080017de   0x00000001   Data   RW          155    .data.g_rs485_tx_complete  rs485_comm.o
    0x2000000f   0x080017df   0x00000001   PAD
    0x20000010        -       0x00000008   Zero   RW           54    .bss..L_MergedGlobals  systick.o
    0x20000018        -       0x0000002c   Zero   RW           90    .bss..L_MergedGlobals  cm1103_driver.o
    0x20000044        -       0x00000028   Zero   RW          161    .bss..L_MergedGlobals  rs485_comm.o
    0x2000006c        -       0x00000020   Zero   RW          156    .bss.tx_buffer      rs485_comm.o
    0x2000008c   0x080017df   0x00000004   PAD
    0x20000090        -       0x00000400   Zero   RW          711    STACK               startup_gd32f3x0.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       764         36          0         10         44       6529   cm1103_driver.o
       332          0          0          0          0       4440   gd32f3x0_gpio.o
        24          0          0          0          0       1120   gd32f3x0_it.o
       116         20          0          0          0       4212   gd32f3x0_misc.o
       936         68          8          0          0      14041   gd32f3x0_rcu.o
       364          8          0          0          0      13454   gd32f3x0_usart.o
       260          8          0          0          0       2120   main.o
      1066         62         34          1         72       8059   rs485_comm.o
       850          4          0          0          0       3477   soft_i2c.o
        36          8        336          0       1024        912   startup_gd32f3x0.o
       696         44          0          4          0       3282   system_gd32f3x0.o
       104         20          0          0          8       3436   systick.o

    ----------------------------------------------------------------------
      5556        <USER>        <GROUP>         16       1152      65082   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         8          0          6          1          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        36          0          0          0          0         68   memcpya.o

    ----------------------------------------------------------------------
       124         <USER>          <GROUP>          0          0        136   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       122         16          0          0          0        136   mc_w.l

    ----------------------------------------------------------------------
       124         <USER>          <GROUP>          0          0        136   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      5680        294        416         16       1152      65262   Grand Totals
      5680        294        416         16       1152      65262   ELF Image Totals
      5680        294        416         16          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 6096 (   5.95kB)
    Total RW  Size (RW Data + ZI Data)              1168 (   1.14kB)
    Total ROM Size (Code + RO Data + RW Data)       6112 (   5.97kB)

==============================================================================

