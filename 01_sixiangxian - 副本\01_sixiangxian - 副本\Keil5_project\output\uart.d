.\output\uart.o: ..\HARDWARE\UART.c
.\output\uart.o: ..\HARDWARE\UART.h
.\output\uart.o: ..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h
.\output\uart.o: ..\CMSIS\core_cm4.h
.\output\uart.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\stdint.h
.\output\uart.o: ..\CMSIS\core_cmInstr.h
.\output\uart.o: ..\CMSIS\core_cmFunc.h
.\output\uart.o: ..\CMSIS\core_cm4_simd.h
.\output\uart.o: ..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h
.\output\uart.o: ..\User\gd32f3x0_libopt.h
.\output\uart.o: ..\Library\Include\gd32f3x0_adc.h
.\output\uart.o: ..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h
.\output\uart.o: ..\Library\Include\gd32f3x0_crc.h
.\output\uart.o: ..\Library\Include\gd32f3x0_ctc.h
.\output\uart.o: ..\Library\Include\gd32f3x0_dbg.h
.\output\uart.o: ..\Library\Include\gd32f3x0_dma.h
.\output\uart.o: ..\Library\Include\gd32f3x0_exti.h
.\output\uart.o: ..\Library\Include\gd32f3x0_fmc.h
.\output\uart.o: ..\Library\Include\gd32f3x0_gpio.h
.\output\uart.o: ..\Library\Include\gd32f3x0_syscfg.h
.\output\uart.o: ..\Library\Include\gd32f3x0_i2c.h
.\output\uart.o: ..\Library\Include\gd32f3x0_fwdgt.h
.\output\uart.o: ..\Library\Include\gd32f3x0_pmu.h
.\output\uart.o: ..\Library\Include\gd32f3x0_rcu.h
.\output\uart.o: ..\Library\Include\gd32f3x0_rtc.h
.\output\uart.o: ..\Library\Include\gd32f3x0_spi.h
.\output\uart.o: ..\Library\Include\gd32f3x0_timer.h
.\output\uart.o: ..\Library\Include\gd32f3x0_usart.h
.\output\uart.o: ..\Library\Include\gd32f3x0_wwdgt.h
.\output\uart.o: ..\Library\Include\gd32f3x0_misc.h
.\output\uart.o: ..\Library\Include\gd32f3x0_tsi.h
.\output\uart.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\stdbool.h
.\output\uart.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\string.h
.\output\uart.o: ..\User\systick.h
.\output\uart.o: ..\HARDWARE\UART.h
.\output\uart.o: ..\User\internal_clock_8m.h
.\output\uart.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\stdio.h
.\output\uart.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\stdarg.h
