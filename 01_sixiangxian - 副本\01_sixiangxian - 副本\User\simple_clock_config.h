/*!
    \file    simple_clock_config.h
    \brief   简化的内部时钟配置头文件 for GD32F3x0
    
    \version 2025-01-01, V1.0.0, firmware for GD32F3x0
*/

#ifndef __SIMPLE_CLOCK_CONFIG_H
#define __SIMPLE_CLOCK_CONFIG_H

#include "gd32f3x0.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 简化的时钟配置选项 */
typedef enum {
    SIMPLE_CLOCK_8M = 0,     /* 8MHz 内部时钟 */
    SIMPLE_CLOCK_72M,        /* 72MHz PLL时钟 */
    SIMPLE_CLOCK_96M         /* 96MHz PLL时钟 */
} simple_clock_freq_t;

/* 函数声明 */

/* 基础配置函数 */
void simple_clock_init(void);
void simple_clock_config(simple_clock_freq_t freq);

/* 获取当前频率 */
uint32_t simple_clock_get_freq(void);

/* 简单延时函数 */
void simple_delay_ms(uint32_t ms);
void simple_delay_us(uint32_t us);

/* 时钟切换函数 */
void simple_clock_switch_to_8m(void);
void simple_clock_switch_to_72m(void);
void simple_clock_switch_to_96m(void);

/* 状态显示 */
void simple_clock_print_info(void);

#ifdef __cplusplus
}
#endif

#endif /* __SIMPLE_CLOCK_CONFIG_H */
