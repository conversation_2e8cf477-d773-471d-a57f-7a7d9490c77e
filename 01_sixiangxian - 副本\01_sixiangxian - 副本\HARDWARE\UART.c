#include "UART.h"
#include <stdio.h>
#include <stdarg.h>
/*
GD32_UART定义/控制例程
配置UART通信格式
*/
// RS485 缓冲区大小
#define RS485_BUFFER_SIZE        64
//GD32_UART初始化
void GD_USART_Config(void)
{
    //使能RCU时钟
    rcu_periph_clock_enable(RCU_USART1);
    /* 使能GPIO时钟 */
    rcu_periph_clock_enable(RCU_GPIOA);
	
	gpio_af_set(GPIOA, GPIO_AF_1, GPIO_PIN_2 | GPIO_PIN_3); 
    /* 设置UART0输出端口 */
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_2 );// 复用模式TX
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_2 );// 推挽输出，50MHz

	gpio_mode_set(GPIOA, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, GPIO_PIN_3 );// 上拉输入 或者 复用模式，浮空输入
	 
	
    /* USART 配置 */
    usart_deinit(USART1);
    usart_baudrate_set(USART1, 115200U);//115200波特率
    usart_word_length_set(USART1, USART_WL_8BIT);//八位
    usart_stop_bit_set(USART1, USART_STB_1BIT);//1位停止
    usart_parity_config(USART1, USART_PM_NONE);//无校验
    usart_hardware_flow_rts_config(USART1, USART_RTS_DISABLE);//无rts流控
    usart_hardware_flow_cts_config(USART1, USART_CTS_DISABLE);//无cts流控
    usart_receive_config(USART1, USART_RECEIVE_ENABLE);//接收配置
    usart_transmit_config(USART1, USART_TRANSMIT_ENABLE);//发射配置
    usart_enable(USART1);//使能串口0
}
// RS485 发送数据
void uart_send_data(uint8_t *data, uint16_t len) 
{
    uint16_t i;
    for( i = 0; i < len; i++)
    {
        
        while (RESET == usart_flag_get(USART1, USART_FLAG_TBE)); // 等待发送完成
        usart_data_transmit(USART1, data[i]);
        //while (RESET == usart_flag_get(RS485_UART, USART_FLAG_TBE)); // 等待发送完成
    }
    while (RESET == usart_flag_get(USART1, USART_FLAG_TC));  // 等待发送完成
}
// 重定向 
void DEBUG(const char *format, ...) 
{
    char buffer[RS485_BUFFER_SIZE];
    va_list args;

    // 格式化字符串
    va_start(args, format);
    vsnprintf(buffer, RS485_BUFFER_SIZE, format, args);
    va_end(args);
    // 通过 RS485 发送数据
    uart_send_data((uint8_t *)buffer, strlen(buffer));
}

/* 重定向printf函数 */
///* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART1, (uint8_t)ch);
    while(RESET == usart_flag_get(USART1, USART_FLAG_TBE));

    return ch;
}


