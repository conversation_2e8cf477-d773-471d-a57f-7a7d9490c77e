<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\output\Project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\output\Project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Sat Aug 02 15:40:10 2025
<BR><P>
<H3>Maximum Stack Usage =        288 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; calculate_high_precision_angle &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[17]">ADC_CMP_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[17]">ADC_CMP_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[17]">ADC_CMP_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[5]">BusFault_Handler</a> from gd32f3x0_it.o(i.BusFault_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[27]">CEC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[14]">DMA_Channel0_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[15]">DMA_Channel1_2_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[16]">DMA_Channel3_4_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2b]">DMA_Channel5_6_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from gd32f3x0_it.o(i.DebugMon_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[10]">EXTI0_1_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[11]">EXTI2_3_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[12]">EXTI4_15_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[e]">FMC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[3]">HardFault_Handler</a> from gd32f3x0_it.o(i.HardFault_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[28]">I2C0_ER_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[21]">I2C0_EV_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[29]">I2C1_ER_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[22]">I2C1_EV_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[c]">LVD_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[4]">MemManage_Handler</a> from gd32f3x0_it.o(i.MemManage_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2]">NMI_Handler</a> from gd32f3x0_it.o(i.NMI_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from gd32f3x0_it.o(i.PendSV_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[f]">RCU_CTC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[d]">RTC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1]">Reset_Handler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[23]">SPI0_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[24]">SPI1_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[7]">SVC_Handler</a> from gd32f3x0_it.o(i.SVC_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[a]">SysTick_Handler</a> from gd32f3x0_it.o(i.SysTick_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2e]">SystemInit</a> from system_gd32f3x0.o(i.SystemInit) referenced from startup_gd32f3x0.o(.text)
 <LI><a href="#[18]">TIMER0_BRK_UP_TRG_COM_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[19]">TIMER0_Channel_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1d]">TIMER13_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1e]">TIMER14_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1f]">TIMER15_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[20]">TIMER16_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1a]">TIMER1_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1b]">TIMER2_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[1c]">TIMER5_DAC_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[13]">TSI_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[25]">USART0_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[26]">USART1_IRQHandler</a> from rs485.o(i.USART1_IRQHandler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2c]">USBFS_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2a]">USBFS_WKUP_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[6]">UsageFault_Handler</a> from gd32f3x0_it.o(i.UsageFault_Handler) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[b]">WWDGT_IRQHandler</a> from startup_gd32f3x0.o(.text) referenced from startup_gd32f3x0.o(RESET)
 <LI><a href="#[2f]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_gd32f3x0.o(.text)
 <LI><a href="#[30]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[2d]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[2f]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(.text)
</UL>
<P><STRONG><a name="[99]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[31]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[43]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[9a]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[9b]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[9c]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[9d]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[9e]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[1]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>ADC_CMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CMP_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CMP_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>CEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>DMA_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA_Channel1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA_Channel3_4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>DMA_Channel5_6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI2_3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI4_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMER0_BRK_UP_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>TIMER14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>TIMER15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>TIMER16_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TSI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f3x0.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG
</UL>

<P><STRONG><a name="[33]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_high_precision_angle
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_hex16
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>

<P><STRONG><a name="[38]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[39]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[3a]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_high_precision_angle
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_hex16
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_ADC
</UL>

<P><STRONG><a name="[3b]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_ddiv &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_high_precision_angle
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_hex16
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[3c]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_ADC
</UL>

<P><STRONG><a name="[3d]"></a>__aeabi_d2uiz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dfixui.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2uiz
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_hex16
</UL>

<P><STRONG><a name="[76]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_high_precision_angle
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_hex16
</UL>

<P><STRONG><a name="[9f]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text), UNUSED)

<P><STRONG><a name="[79]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_high_precision_angle
</UL>

<P><STRONG><a name="[3f]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_ADC
</UL>

<P><STRONG><a name="[a0]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[74]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[41]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[34]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[a1]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[3e]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[a2]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[35]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[a3]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[a4]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[40]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[a5]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text), UNUSED)

<P><STRONG><a name="[37]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[36]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>

<P><STRONG><a name="[42]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[71]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[32]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[a6]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[44]"></a>ADS1115_Read_ADC</STRONG> (Thumb, 374 bytes, Stack size 48 bytes, ads1115.o(i.ADS1115_Read_ADC))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ADS1115_Read_ADC &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Write
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_adc
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_average_ADC
</UL>

<P><STRONG><a name="[47]"></a>ADS1115_Read_adc</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, ads1115.o(i.ADS1115_Read_adc))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ADS1115_Read_adc &rArr; IIC_Read_Byte &rArr; IIC_NAck &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_ReceiveAck
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_SendByte
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_average_ADC
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_ADC
</UL>

<P><STRONG><a name="[4d]"></a>ADS1115_Read_average_ADC</STRONG> (Thumb, 268 bytes, Stack size 88 bytes, ads1115.o(i.ADS1115_Read_average_ADC))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = ADS1115_Read_average_ADC &rArr; ADS1115_Read_ADC &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_adc
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_ADC
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;high_precision_adc_read
</UL>

<P><STRONG><a name="[45]"></a>ADS1115_Write</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, ads1115.o(i.ADS1115_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ADS1115_Write &rArr; IIC_SendByte &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_ADC
</UL>

<P><STRONG><a name="[5]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DEBUG</STRONG> (Thumb, 50 bytes, Stack size 96 bytes, uart.o(i.DEBUG))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = DEBUG &rArr; vsnprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_send_data
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>GD_Led_Config</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, led.o(i.GD_Led_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = GD_Led_Config &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[3]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>IIC_Ack</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, myiic.o(i.IIC_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IIC_Ack &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>

<P><STRONG><a name="[5b]"></a>IIC_Init</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, myiic.o(i.IIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = IIC_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5c]"></a>IIC_NAck</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, myiic.o(i.IIC_NAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IIC_NAck &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>

<P><STRONG><a name="[4b]"></a>IIC_Read_Byte</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, myiic.o(i.IIC_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = IIC_Read_Byte &rArr; IIC_NAck &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_adc
</UL>

<P><STRONG><a name="[49]"></a>IIC_SendByte</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, myiic.o(i.IIC_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = IIC_SendByte &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Write
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_adc
</UL>

<P><STRONG><a name="[48]"></a>IIC_Start</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, myiic.o(i.IIC_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IIC_Start &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Write
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_adc
</UL>

<P><STRONG><a name="[4c]"></a>IIC_Stop</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, myiic.o(i.IIC_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IIC_Stop &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Write
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_adc
</UL>

<P><STRONG><a name="[4e]"></a>IIC_Wait_Ack</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, myiic.o(i.IIC_Wait_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = IIC_Wait_Ack &rArr; IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Write
</UL>

<P><STRONG><a name="[5e]"></a>Led_On</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, led.o(i.Led_On))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Led_On
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SystemInit</STRONG> (Thumb, 648 bytes, Stack size 16 bytes, system_gd32f3x0.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SystemInit &rArr; system_clock_config &rArr; system_clock_72m_hxtal
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_vector_table_set
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_soft_delay_
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(.text)
</UL>
<P><STRONG><a name="[26]"></a>USART1_IRQHandler</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, rs485.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_IRQHandler &rArr; usart_interrupt_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_get
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_flag_clear
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_receive
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f3x0.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>__0vsnprintf</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[a7]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[a8]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[a9]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[50]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG
</UL>

<P><STRONG><a name="[69]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>

<P><STRONG><a name="[67]"></a>__hardfp_atan</STRONG> (Thumb, 622 bytes, Stack size 48 bytes, atan.o(i.__hardfp_atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[6d]"></a>__hardfp_atan2</STRONG> (Thumb, 432 bytes, Stack size 48 bytes, atan2.o(i.__hardfp_atan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_high_precision_angle
</UL>

<P><STRONG><a name="[78]"></a>__hardfp_fabs</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, fabs.o(i.__hardfp_fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __hardfp_fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_high_precision_angle
</UL>

<P><STRONG><a name="[6c]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>

<P><STRONG><a name="[68]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>

<P><STRONG><a name="[6e]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[6a]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>

<P><STRONG><a name="[aa]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[ab]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[ac]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[75]"></a>angle_to_hex16</STRONG> (Thumb, 94 bytes, Stack size 48 bytes, main.o(i.angle_to_hex16))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = angle_to_hex16 &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6f]"></a>atan</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, atan.o(i.atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[7e]"></a>calculate_elevation_angle_calibrated</STRONG> (Thumb, 426 bytes, Stack size 0 bytes, main.o(i.calculate_elevation_angle_calibrated))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[77]"></a>calculate_high_precision_angle</STRONG> (Thumb, 256 bytes, Stack size 64 bytes, main.o(i.calculate_high_precision_angle))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = calculate_high_precision_angle &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_fabs
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7b]"></a>delay_ms</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, systick.o(i.delay_ms))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;high_precision_adc_read
</UL>

<P><STRONG><a name="[46]"></a>delay_us</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, systick.o(i.delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_ADC
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_ReceiveAck
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_delay
</UL>

<P><STRONG><a name="[7f]"></a>elevation_to_hex16</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, main.o(i.elevation_to_hex16))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6b]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2
</UL>

<P><STRONG><a name="[83]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f3x0_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[57]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD_Led_Config
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_set_rx_mode
</UL>

<P><STRONG><a name="[93]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f3x0_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_set_tx_mode
</UL>

<P><STRONG><a name="[59]"></a>gpio_bit_write</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f3x0_gpio.o(i.gpio_bit_write))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_On
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_ReceiveAck
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_SendByte
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
</UL>

<P><STRONG><a name="[5d]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f3x0_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
</UL>

<P><STRONG><a name="[55]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f3x0_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD_Led_Config
</UL>

<P><STRONG><a name="[56]"></a>gpio_output_options_set</STRONG> (Thumb, 124 bytes, Stack size 20 bytes, gd32f3x0_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD_Led_Config
</UL>

<P><STRONG><a name="[7a]"></a>high_precision_adc_read</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, main.o(i.high_precision_adc_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = high_precision_adc_read &rArr; ADS1115_Read_average_ADC &rArr; ADS1115_Read_ADC &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_average_ADC
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2d]"></a>main</STRONG> (Thumb, 282 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = main &rArr; calculate_high_precision_angle &rArr; __hardfp_atan2 &rArr; atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Led_On
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD_Led_Config
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;high_precision_adc_read
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;elevation_to_hex16
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_high_precision_angle
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_elevation_angle_calibrated
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;angle_to_hex16
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[81]"></a>nvic_irq_enable</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, gd32f3x0_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[82]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f3x0_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[61]"></a>nvic_vector_table_set</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f3x0_misc.o(i.nvic_vector_table_set))
<BR><BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[96]"></a>rcu_clock_freq_get</STRONG> (Thumb, 628 bytes, Stack size 108 bytes, gd32f3x0_rcu.o(i.rcu_clock_freq_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
</UL>

<P><STRONG><a name="[54]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Init
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GD_Led_Config
</UL>

<P><STRONG><a name="[98]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[97]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, gd32f3x0_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
</UL>

<P><STRONG><a name="[7c]"></a>rs485_init</STRONG> (Thumb, 226 bytes, Stack size 8 bytes, rs485.o(i.rs485_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = rs485_init &rArr; usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_transmit_config
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_receive_config
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_enable
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_deinit
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_baudrate_set
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_word_length_set
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_stop_bit_set
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_parity_config
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_interrupt_enable
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_rts_config
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_hardware_flow_cts_config
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>rs485_send_data</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, rs485.o(i.rs485_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = rs485_send_data &rArr; rs485_set_tx_mode
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_set_tx_mode
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_set_rx_mode
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[92]"></a>rs485_set_rx_mode</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, rs485.o(i.rs485_set_rx_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rs485_set_rx_mode
</UL>
<BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
</UL>

<P><STRONG><a name="[8f]"></a>rs485_set_tx_mode</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, rs485.o(i.rs485_set_tx_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rs485_set_tx_mode
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
</UL>

<P><STRONG><a name="[95]"></a>systick_clksource_set</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, gd32f3x0_misc.o(i.systick_clksource_set))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_config
</UL>

<P><STRONG><a name="[7d]"></a>systick_config</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, systick.o(i.systick_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = systick_config &rArr; DEBUG &rArr; vsnprintf
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;systick_clksource_set
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[52]"></a>uart_send_data</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, uart.o(i.uart_send_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = uart_send_data &rArr; usart_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_flag_get
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usart_data_transmit
</UL>
<BR>[Called By]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DEBUG
</UL>

<P><STRONG><a name="[85]"></a>usart_baudrate_set</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, gd32f3x0_usart.o(i.usart_baudrate_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = usart_baudrate_set &rArr; rcu_clock_freq_get
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_clock_freq_get
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[64]"></a>usart_data_receive</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[91]"></a>usart_data_transmit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_send_data
</UL>

<P><STRONG><a name="[84]"></a>usart_deinit</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, gd32f3x0_usart.o(i.usart_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[8e]"></a>usart_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_enable))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[90]"></a>usart_flag_get</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, gd32f3x0_usart.o(i.usart_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_send_data
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_send_data
</UL>

<P><STRONG><a name="[8a]"></a>usart_hardware_flow_cts_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_hardware_flow_cts_config))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[89]"></a>usart_hardware_flow_rts_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_hardware_flow_rts_config))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[8d]"></a>usart_interrupt_enable</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, gd32f3x0_usart.o(i.usart_interrupt_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[63]"></a>usart_interrupt_flag_clear</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, gd32f3x0_usart.o(i.usart_interrupt_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usart_interrupt_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[62]"></a>usart_interrupt_flag_get</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, gd32f3x0_usart.o(i.usart_interrupt_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usart_interrupt_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[88]"></a>usart_parity_config</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_parity_config))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[8b]"></a>usart_receive_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_receive_config))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[87]"></a>usart_stop_bit_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_stop_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[8c]"></a>usart_transmit_config</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_transmit_config))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>

<P><STRONG><a name="[86]"></a>usart_word_length_set</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f3x0_usart.o(i.usart_word_length_set))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rs485_init
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[5f]"></a>_soft_delay_</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, system_gd32f3x0.o(i._soft_delay_))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _soft_delay_
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[94]"></a>system_clock_72m_hxtal</STRONG> (Thumb, 196 bytes, Stack size 8 bytes, system_gd32f3x0.o(i.system_clock_72m_hxtal))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_72m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[60]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f3x0.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = system_clock_config &rArr; system_clock_72m_hxtal
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_72m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[5a]"></a>IIC_delay</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, myiic.o(i.IIC_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IIC_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Wait_Ack
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Stop
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Start
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_SendByte
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Read_Byte
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_NAck
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;IIC_Ack
</UL>

<P><STRONG><a name="[4a]"></a>IIC_ReceiveAck</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, ads1115.o(i.IIC_ReceiveAck))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = IIC_ReceiveAck
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_write
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADS1115_Read_adc
</UL>

<P><STRONG><a name="[70]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[66]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
</UL>

<P><STRONG><a name="[73]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[72]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[30]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsnprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
