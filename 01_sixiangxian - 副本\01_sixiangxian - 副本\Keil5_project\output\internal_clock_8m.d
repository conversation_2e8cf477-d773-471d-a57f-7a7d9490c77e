.\output\internal_clock_8m.o: ..\User\internal_clock_8m.c
.\output\internal_clock_8m.o: ..\User\internal_clock_8m.h
.\output\internal_clock_8m.o: ..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h
.\output\internal_clock_8m.o: ..\CMSIS\core_cm4.h
.\output\internal_clock_8m.o: C:\KEILmdk\ARM\ARMCC\Bin\..\include\stdint.h
.\output\internal_clock_8m.o: ..\CMSIS\core_cmInstr.h
.\output\internal_clock_8m.o: ..\CMSIS\core_cmFunc.h
.\output\internal_clock_8m.o: ..\CMSIS\core_cm4_simd.h
.\output\internal_clock_8m.o: ..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h
.\output\internal_clock_8m.o: ..\User\gd32f3x0_libopt.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_adc.h
.\output\internal_clock_8m.o: ..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_crc.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_ctc.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_dbg.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_dma.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_exti.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_fmc.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_gpio.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_syscfg.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_i2c.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_fwdgt.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_pmu.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_rcu.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_rtc.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_spi.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_timer.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_usart.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_wwdgt.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_misc.h
.\output\internal_clock_8m.o: ..\Library\Include\gd32f3x0_tsi.h
.\output\internal_clock_8m.o: ..\HARDWARE\UART.h
.\output\internal_clock_8m.o: C:\KEILmdk\ARM\ARMCC\Bin\..\include\stdbool.h
.\output\internal_clock_8m.o: C:\KEILmdk\ARM\ARMCC\Bin\..\include\string.h
.\output\internal_clock_8m.o: ..\User\systick.h
.\output\internal_clock_8m.o: ..\HARDWARE\UART.h
