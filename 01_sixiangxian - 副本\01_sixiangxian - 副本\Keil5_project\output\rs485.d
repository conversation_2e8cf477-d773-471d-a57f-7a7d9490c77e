.\output\rs485.o: ..\HARDWARE\RS485.c
.\output\rs485.o: ..\HARDWARE\RS485.h
.\output\rs485.o: ..\User\systick.h
.\output\rs485.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\stdint.h
.\output\rs485.o: ..\HARDWARE\UART.h
.\output\rs485.o: ..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h
.\output\rs485.o: ..\CMSIS\core_cm4.h
.\output\rs485.o: ..\CMSIS\core_cmInstr.h
.\output\rs485.o: ..\CMSIS\core_cmFunc.h
.\output\rs485.o: ..\CMSIS\core_cm4_simd.h
.\output\rs485.o: ..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h
.\output\rs485.o: ..\User\gd32f3x0_libopt.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_adc.h
.\output\rs485.o: ..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_crc.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_ctc.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_dbg.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_dma.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_exti.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_fmc.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_gpio.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_syscfg.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_i2c.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_fwdgt.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_pmu.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_rcu.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_rtc.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_spi.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_timer.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_usart.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_wwdgt.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_misc.h
.\output\rs485.o: ..\Library\Include\gd32f3x0_tsi.h
.\output\rs485.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\stdbool.h
.\output\rs485.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\string.h
.\output\rs485.o: ..\User\systick.h
.\output\rs485.o: ..\User\internal_clock_8m.h
.\output\rs485.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\stdio.h
.\output\rs485.o: D:\Keil5.27\Core\ARM\ARMCC\Bin\..\include\stdarg.h
.\output\rs485.o: ..\HARDWARE\crc16.h
