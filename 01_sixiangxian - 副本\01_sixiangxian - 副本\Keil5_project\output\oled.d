.\output\oled.o: ..\HARDWARE\oled.c
.\output\oled.o: ..\HARDWARE\oled.h
.\output\oled.o: ..\User\systick.h
.\output\oled.o: C:\KEILmdk\ARM\ARMCC\Bin\..\include\stdint.h
.\output\oled.o: C:\KEILmdk\ARM\ARMCC\Bin\..\include\stdlib.h
.\output\oled.o: ..\HARDWARE\OLED_Font.h
.\output\oled.o: ..\HARDWARE\IIC\myiic.h
.\output\oled.o: ..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h
.\output\oled.o: ..\CMSIS\core_cm4.h
.\output\oled.o: ..\CMSIS\core_cmInstr.h
.\output\oled.o: ..\CMSIS\core_cmFunc.h
.\output\oled.o: ..\CMSIS\core_cm4_simd.h
.\output\oled.o: ..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h
.\output\oled.o: ..\User\gd32f3x0_libopt.h
.\output\oled.o: ..\Library\Include\gd32f3x0_adc.h
.\output\oled.o: ..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h
.\output\oled.o: ..\Library\Include\gd32f3x0_crc.h
.\output\oled.o: ..\Library\Include\gd32f3x0_ctc.h
.\output\oled.o: ..\Library\Include\gd32f3x0_dbg.h
.\output\oled.o: ..\Library\Include\gd32f3x0_dma.h
.\output\oled.o: ..\Library\Include\gd32f3x0_exti.h
.\output\oled.o: ..\Library\Include\gd32f3x0_fmc.h
.\output\oled.o: ..\Library\Include\gd32f3x0_gpio.h
.\output\oled.o: ..\Library\Include\gd32f3x0_syscfg.h
.\output\oled.o: ..\Library\Include\gd32f3x0_i2c.h
.\output\oled.o: ..\Library\Include\gd32f3x0_fwdgt.h
.\output\oled.o: ..\Library\Include\gd32f3x0_pmu.h
.\output\oled.o: ..\Library\Include\gd32f3x0_rcu.h
.\output\oled.o: ..\Library\Include\gd32f3x0_rtc.h
.\output\oled.o: ..\Library\Include\gd32f3x0_spi.h
.\output\oled.o: ..\Library\Include\gd32f3x0_timer.h
.\output\oled.o: ..\Library\Include\gd32f3x0_usart.h
.\output\oled.o: ..\Library\Include\gd32f3x0_wwdgt.h
.\output\oled.o: ..\Library\Include\gd32f3x0_misc.h
.\output\oled.o: ..\Library\Include\gd32f3x0_tsi.h
