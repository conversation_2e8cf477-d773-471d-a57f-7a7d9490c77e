--cpu=Cortex-M4.fp
".\output\main.o"
".\output\gd32f3x0_it.o"
".\output\systick.o"
".\output\system_gd32f3x0.o"
".\output\startup_gd32f3x0.o"
".\output\gd32f350r_eval.o"
".\output\gd32f3x0_adc.o"
".\output\gd32f3x0_cec.o"
".\output\gd32f3x0_cmp.o"
".\output\gd32f3x0_crc.o"
".\output\gd32f3x0_ctc.o"
".\output\gd32f3x0_dac.o"
".\output\gd32f3x0_dbg.o"
".\output\gd32f3x0_dma.o"
".\output\gd32f3x0_exti.o"
".\output\gd32f3x0_fmc.o"
".\output\gd32f3x0_fwdgt.o"
".\output\gd32f3x0_gpio.o"
".\output\gd32f3x0_i2c.o"
".\output\gd32f3x0_misc.o"
".\output\gd32f3x0_pmu.o"
".\output\gd32f3x0_rcu.o"
".\output\gd32f3x0_rtc.o"
".\output\gd32f3x0_spi.o"
".\output\gd32f3x0_syscfg.o"
".\output\gd32f3x0_timer.o"
".\output\gd32f3x0_tsi.o"
".\output\gd32f3x0_usart.o"
".\output\gd32f3x0_wwdgt.o"
".\output\uart.o"
".\output\led.o"
".\output\myiic.o"
".\output\ads1115.o"
".\output\crc16.o"
".\output\rs485.o"
--library_type=microlib --strict --scatter ".\output\Project.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\list\Project.map" -o .\output\Project.axf