Dependencies for Project 'Project', Target 'GD32F310': (DO NOT MODIFY !)
F (..\User\main.c)(0x684D6B8E)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\main.o --omf_browse .\output\main.crf --depend .\output\main.d)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (..\User\systick.h)(0x68416056)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\internal_clock_8m.h)(0x684163CE)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\Keil5.27\Core\ARM\ARMCC\include\math.h)(0x599ECD2E)
I (..\User\main.h)(0x68493AC4)
I (..\HARDWARE\led.h)(0x68414E86)
I (..\HARDWARE\IIC\myiic.h)(0x682C107A)
I (..\HARDWARE\ads1115.h)(0x684D5D26)
I (..\HARDWARE\RS485.h)(0x68493FCA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdarg.h)(0x599ECD2A)
I (..\HARDWARE\crc16.h)(0x68453452)
F (..\User\gd32f3x0_it.c)(0x68415308)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_it.o --omf_browse .\output\gd32f3x0_it.crf --depend .\output\gd32f3x0_it.d)
I (..\User\gd32f3x0_it.h)(0x67BD7BE0)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (..\User\main.h)(0x68493AC4)
I (..\User\systick.h)(0x68416056)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\internal_clock_8m.h)(0x684163CE)
F (..\User\systick.c)(0x6848DFD6)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\systick.o --omf_browse .\output\systick.crf --depend .\output\systick.d)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (..\User\systick.h)(0x68416056)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\internal_clock_8m.h)(0x684163CE)
F (..\CMSIS\GD\GD32F3x0\Source\system_gd32f3x0.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\system_gd32f3x0.o --omf_browse .\output\system_gd32f3x0.crf --depend .\output\system_gd32f3x0.d)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\CMSIS\GD\GD32F3x0\Source\ARM\startup_gd32f3x0.s)(0x67BD7BDA)(--cpu Cortex-M4.fp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

--pd "__UVISION_VERSION SETA 527" --pd "GD32F310 SETA 1"

--list .\list\startup_gd32f3x0.lst --xref -o .\output\startup_gd32f3x0.o --depend .\output\startup_gd32f3x0.d)
F (..\gd32f350r_eval.c)(0x681B3EC0)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f350r_eval.o --omf_browse .\output\gd32f350r_eval.crf --depend .\output\gd32f350r_eval.d)
I (..\gd32f350r_eval.h)(0x67BD7BE0)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\User\readme.txt)(0x67BD7BE0)()
F (..\Library\Source\gd32f3x0_adc.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_adc.o --omf_browse .\output\gd32f3x0_adc.crf --depend .\output\gd32f3x0_adc.d)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_cec.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_cec.o --omf_browse .\output\gd32f3x0_cec.crf --depend .\output\gd32f3x0_cec.d)
F (..\Library\Source\gd32f3x0_cmp.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_cmp.o --omf_browse .\output\gd32f3x0_cmp.crf --depend .\output\gd32f3x0_cmp.d)
I (..\Library\Include\gd32f3x0_cmp.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_crc.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_crc.o --omf_browse .\output\gd32f3x0_crc.crf --depend .\output\gd32f3x0_crc.d)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_ctc.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_ctc.o --omf_browse .\output\gd32f3x0_ctc.crf --depend .\output\gd32f3x0_ctc.d)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_dac.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_dac.o --omf_browse .\output\gd32f3x0_dac.crf --depend .\output\gd32f3x0_dac.d)
F (..\Library\Source\gd32f3x0_dbg.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_dbg.o --omf_browse .\output\gd32f3x0_dbg.crf --depend .\output\gd32f3x0_dbg.d)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_dma.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_dma.o --omf_browse .\output\gd32f3x0_dma.crf --depend .\output\gd32f3x0_dma.d)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_exti.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_exti.o --omf_browse .\output\gd32f3x0_exti.crf --depend .\output\gd32f3x0_exti.d)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_fmc.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_fmc.o --omf_browse .\output\gd32f3x0_fmc.crf --depend .\output\gd32f3x0_fmc.d)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_fwdgt.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_fwdgt.o --omf_browse .\output\gd32f3x0_fwdgt.crf --depend .\output\gd32f3x0_fwdgt.d)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_gpio.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_gpio.o --omf_browse .\output\gd32f3x0_gpio.crf --depend .\output\gd32f3x0_gpio.d)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_i2c.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_i2c.o --omf_browse .\output\gd32f3x0_i2c.crf --depend .\output\gd32f3x0_i2c.d)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_misc.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_misc.o --omf_browse .\output\gd32f3x0_misc.crf --depend .\output\gd32f3x0_misc.d)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_pmu.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_pmu.o --omf_browse .\output\gd32f3x0_pmu.crf --depend .\output\gd32f3x0_pmu.d)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_rcu.c)(0x68400216)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_rcu.o --omf_browse .\output\gd32f3x0_rcu.crf --depend .\output\gd32f3x0_rcu.d)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_rtc.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_rtc.o --omf_browse .\output\gd32f3x0_rtc.crf --depend .\output\gd32f3x0_rtc.d)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_spi.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_spi.o --omf_browse .\output\gd32f3x0_spi.crf --depend .\output\gd32f3x0_spi.d)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_syscfg.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_syscfg.o --omf_browse .\output\gd32f3x0_syscfg.crf --depend .\output\gd32f3x0_syscfg.d)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_timer.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_timer.o --omf_browse .\output\gd32f3x0_timer.crf --depend .\output\gd32f3x0_timer.d)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_tsi.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_tsi.o --omf_browse .\output\gd32f3x0_tsi.crf --depend .\output\gd32f3x0_tsi.d)
F (..\Library\Source\gd32f3x0_usart.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_usart.o --omf_browse .\output\gd32f3x0_usart.crf --depend .\output\gd32f3x0_usart.d)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\Library\Source\gd32f3x0_wwdgt.c)(0x67BD7BDA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\gd32f3x0_wwdgt.o --omf_browse .\output\gd32f3x0_wwdgt.crf --depend .\output\gd32f3x0_wwdgt.d)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
F (..\HARDWARE\UART.c)(0x68415390)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\uart.o --omf_browse .\output\uart.crf --depend .\output\uart.d)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\systick.h)(0x68416056)
I (..\User\internal_clock_8m.h)(0x684163CE)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdarg.h)(0x599ECD2A)
F (..\HARDWARE\led.c)(0x68414E94)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\led.o --omf_browse .\output\led.crf --depend .\output\led.d)
I (..\HARDWARE\led.h)(0x68414E86)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (..\User\systick.h)(0x68416056)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\internal_clock_8m.h)(0x684163CE)
F (..\HARDWARE\IIC\myiic.c)(0x682C106E)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\myiic.o --omf_browse .\output\myiic.crf --depend .\output\myiic.d)
I (..\HARDWARE\IIC\myiic.h)(0x682C107A)
I (..\User\systick.h)(0x68416056)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\internal_clock_8m.h)(0x684163CE)
F (..\HARDWARE\ads1115.c)(0x684D5D26)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\ads1115.o --omf_browse .\output\ads1115.crf --depend .\output\ads1115.d)
I (..\HARDWARE\IIC\myiic.h)(0x682C107A)
I (..\User\systick.h)(0x68416056)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\internal_clock_8m.h)(0x684163CE)
I (..\HARDWARE\ads1115.h)(0x684D5D26)
F (..\HARDWARE\crc16.c)(0x67DA77EA)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\crc16.o --omf_browse .\output\crc16.crf --depend .\output\crc16.d)
I (..\HARDWARE\crc16.h)(0x68453452)
I (..\User\systick.h)(0x68416056)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\internal_clock_8m.h)(0x684163CE)
F (..\HARDWARE\RS485.c)(0x684D2A4E)(--c99 -c --cpu Cortex-M4.fp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\CMSIS\GD\GD32F3x0\Include -I ..\CMSIS\GD\GD32F3x0\Source\ARM -I ..\Library -I ..\Library\Include -I ..\Library\Source -I ..\User -I ..\HARDWARE -I ..\HARDWARE -I ..\HARDWARE\IIC

-I.\RTE\_GD32F310

-ID:\Keil5.27\Pack\GigaDevice\GD32F3x0_DFP\3.3.0\Device\Include

-ID:\Keil5.27\Core\ARM\CMSIS\Include

-D__UVISION_VERSION="527" -DGD32F310 -DUSE_STDPERIPH_DRIVER -DGD32F3X0 -DGD32F310

-o .\output\rs485.o --omf_browse .\output\rs485.crf --depend .\output\rs485.d)
I (..\HARDWARE\RS485.h)(0x68493FCA)
I (..\User\systick.h)(0x68416056)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\HARDWARE\UART.h)(0x681DE8E2)
I (..\CMSIS\GD\GD32F3x0\Include\gd32f3x0.h)(0x6845363A)
I (..\CMSIS\core_cm4.h)(0x67BD7BDA)
I (..\CMSIS\core_cmInstr.h)(0x67BD7BDA)
I (..\CMSIS\core_cmFunc.h)(0x67BD7BDA)
I (..\CMSIS\core_cm4_simd.h)(0x67BD7BDA)
I (..\CMSIS\GD\GD32F3x0\Include\system_gd32f3x0.h)(0x67BD7BDA)
I (..\User\gd32f3x0_libopt.h)(0x67BD7BE0)
I (..\Library\Include\gd32f3x0_adc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_crc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_ctc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dbg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_dma.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_exti.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fmc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_gpio.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_syscfg.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_i2c.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_fwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_pmu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rcu.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_rtc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_spi.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_timer.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_usart.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_wwdgt.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_misc.h)(0x67BD7BDA)
I (..\Library\Include\gd32f3x0_tsi.h)(0x67BD7BDA)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdbool.h)(0x599ECD2E)
I (D:\Keil5.27\Core\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\User\internal_clock_8m.h)(0x684163CE)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\Keil5.27\Core\ARM\ARMCC\include\stdarg.h)(0x599ECD2A)
I (..\HARDWARE\crc16.h)(0x68453452)
