/*!
    \file    internal_clock_8m.c
    \brief   简单的内部8MHz时钟配置源文件 for GD32F3x0
    
    \version 2025-01-01, V1.0.0, firmware for GD32F3x0
*/

#include "internal_clock_8m.h"
#include "UART.h"

/*!
    \brief      配置系统时钟为内部8MHz IRC8M
    \param[in]  none
    \param[out] none
    \retval     none
*/
void internal_clock_8m_init(void)
{
    /* 启用IRC8M */
    rcu_osci_on(RCU_IRC8M);

    /* 等待IRC8M稳定 */
    while(SUCCESS != rcu_osci_stab_wait(RCU_IRC8M)) {
        /* 等待时钟稳定 */
    }

    /* 配置总线分频 */
    rcu_ahb_clock_config(RCU_AHB_CKSYS_DIV1);      /* AHB = SYSCLK = 8MHz */
    rcu_apb1_clock_config(RCU_APB1_CKAHB_DIV1);    /* APB1 = AHB = 8MHz */
    rcu_apb2_clock_config(RCU_APB2_CKAHB_DIV1);    /* APB2 = AHB = 8MHz */

    /* 切换系统时钟到IRC8M */
    rcu_system_clock_source_config(RCU_CKSYSSRC_IRC8M);

    /* 等待切换完成 */
    while(RCU_SCSS_IRC8M != rcu_system_clock_source_get()) {
        /* 等待时钟切换完成 */
    }

    /* 更新系统时钟频率变量 */
    SystemCoreClockUpdate();

    /* 关闭外部时钟以节省功耗 */
    rcu_osci_off(RCU_HXTAL);

    /* 配置SysTick时钟源为内部时钟HCLK */
    systick_clksource_set(SYSTICK_CLKSOURCE_HCLK);  /* SysTick使用内部时钟HCLK = 8MHz */

    DEBUG("Internal 8MHz clock configured successfully\r\n");
    DEBUG("System frequency: %d Hz\r\n", SystemCoreClock);
    DEBUG("External clock disabled, SysTick using internal clock\r\n");
}

/*!
    \brief      配置系统时钟为内部IRC8M通过PLL达到72MHz
    \param[in]  none
    \param[out] none
    \retval     none
*/
void internal_clock_max_freq_init(void)
{
    /* 启用IRC8M */
    rcu_osci_on(RCU_IRC8M);

    /* 等待IRC8M稳定 */
    while(SUCCESS != rcu_osci_stab_wait(RCU_IRC8M)) {
        /* 等待时钟稳定 */
    }

    /* 配置总线分频 */
    rcu_ahb_clock_config(RCU_AHB_CKSYS_DIV1);      /* AHB = SYSCLK = 72MHz */
    rcu_apb1_clock_config(RCU_APB1_CKAHB_DIV2);    /* APB1 = AHB/2 = 36MHz */
    rcu_apb2_clock_config(RCU_APB2_CKAHB_DIV1);    /* APB2 = AHB = 72MHz */

    /* 配置PLL: IRC8M/2 * 18 = 4MHz * 18 = 72MHz */
    rcu_pll_config(RCU_PLLSRC_IRC8M_DIV2, RCU_PLL_MUL18);

    /* 启用PLL */
    rcu_osci_on(RCU_PLL_CK);

    /* 等待PLL稳定 */
    while(SUCCESS != rcu_osci_stab_wait(RCU_PLL_CK)) {
        /* 等待PLL稳定 */
    }

    /* 切换系统时钟到PLL */
    rcu_system_clock_source_config(RCU_CKSYSSRC_PLL);

    /* 等待切换完成 */
    while(RCU_SCSS_PLL != rcu_system_clock_source_get()) {
        /* 等待时钟切换完成 */
    }

    /* 更新系统时钟频率变量 */
    SystemCoreClockUpdate();

    /* 关闭外部时钟以节省功耗 */
    rcu_osci_off(RCU_HXTAL);

    /* 配置SysTick时钟源为内部时钟HCLK */
    systick_clksource_set(SYSTICK_CLKSOURCE_HCLK);  /* SysTick使用内部时钟HCLK = 72MHz */

    DEBUG("System frequency: %d Hz (%d MHz)\r\n", SystemCoreClock, SystemCoreClock/1000000);
}

/*!
    \brief      毫秒延时函数（优化for 72MHz时钟）
    \param[in]  ms: 延时毫秒数
    \param[out] none
    \retval     none
*/
void delay_ms(uint32_t ms)
{
    uint32_t i;

    /* 使用SysTick进行精确延时 */
    for(i = 0; i < ms; i++) {
        internal_delay_us_systick(1000);  /* 1毫秒 = 1000微秒 */
    }
}

/*!
    \brief      微秒延时函数（优化for 72MHz时钟）
    \param[in]  us: 延时微秒数
    \param[out] none
    \retval     none
*/
void delay_us(uint32_t us)
{
    internal_delay_us_systick(us);
}

/*!
    \brief      基于循环的毫秒延时函数（备用方案）
    \param[in]  ms: 延时毫秒数
    \param[out] none
    \retval     none
*/
void internal_delay_ms_loop(uint32_t ms)
{
    uint32_t i, j;

    /* 针对72MHz优化的循环延时 */
    /* 每毫秒大约需要18000次循环 (72MHz / 4000 ≈ 18000) */
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 18000; j++) {
            __NOP();  /* 空操作指令 */
        }
    }
}

/*!
    \brief      基于循环的微秒延时函数（备用方案）
    \param[in]  us: 延时微秒数
    \param[out] none
    \retval     none
*/
void internal_delay_us_loop(uint32_t us)
{
    uint32_t i, j;

    /* 针对72MHz优化的循环延时 */
    /* 每微秒大约需要18次循环 (72MHz / 4000000 ≈ 18) */
    for(i = 0; i < us; i++) {
        for(j = 0; j < 18; j++) {
            __NOP();  /* 空操作指令 */
        }
    }
}

/*!
    \brief      获取当前系统时钟频率
    \param[in]  none
    \param[out] none
    \retval     系统时钟频率（Hz）
*/
uint32_t internal_get_system_freq(void)
{
    return SystemCoreClock;
}

/*!
    \brief      基于SysTick的精确毫秒延时（针对72MHz优化）
    \param[in]  ms: 延时毫秒数
    \param[out] none
    \retval     none
*/
void internal_delay_ms_systick(uint32_t ms)
{
    uint32_t i;
    uint32_t reload_value;

    /* 计算1ms对应的SysTick重载值
       使用HCLK作为时钟源：72MHz / 1000 = 72000 */
    reload_value = SystemCoreClock / 1000;

    for(i = 0; i < ms; i++) {
        /* 配置SysTick */
        SysTick->LOAD = reload_value - 1;  /* 重载值 */
        SysTick->VAL = 0;                  /* 清除当前值 */
        /* 启用SysTick，使用处理器时钟HCLK（不是HCLK/8） */
        SysTick->CTRL = SysTick_CTRL_ENABLE_Msk | SysTick_CTRL_CLKSOURCE_Msk;

        /* 等待SysTick计数到0 */
        while(!(SysTick->CTRL & SysTick_CTRL_COUNTFLAG_Msk)) {
            /* 等待计数完成 */
        }

        /* 禁用SysTick */
        SysTick->CTRL = 0;
    }
}

/*!
    \brief      基于SysTick的精确微秒延时（针对72MHz优化）
    \param[in]  us: 延时微秒数
    \param[out] none
    \retval     none
*/

void internal_delay_us_systick(uint32_t us)
{
    uint32_t ticks;
    uint32_t start_val, current_val, elapsed_ticks;

    if(us == 0) return;

    /* 计算需要的时钟周期数
       使用HCLK作为时钟源：72MHz / 1000000 = 72 ticks per us */
    ticks = (SystemCoreClock / 1000000) * us;

    /* 如果延时太小，使用循环延时 */
    if(ticks < 10) {
        volatile uint32_t i;
        for(i = 0; i < ticks * 2; i++) {
            __NOP();
        }
        return;
    }

    /* 配置SysTick */
    SysTick->LOAD = 0xFFFFFF;  /* 最大重载值 */
    SysTick->VAL = 0;          /* 清除当前值 */
    /* 启用SysTick，使用处理器时钟HCLK（不是HCLK/8） */
    SysTick->CTRL = SysTick_CTRL_ENABLE_Msk | SysTick_CTRL_CLKSOURCE_Msk;

    start_val = SysTick->VAL;

    do {
        current_val = SysTick->VAL;
        /* SysTick是递减计数器，处理回绕 */
        if(current_val <= start_val) {
            elapsed_ticks = start_val - current_val;
        } else {
            elapsed_ticks = start_val + (0xFFFFFF - current_val);
        }
    } while(elapsed_ticks < ticks);

    /* 禁用SysTick */
    SysTick->CTRL = 0;
}

/*!
    \brief      简单的延时函数（基于循环计数）
    \param[in]  count: 循环次数
    \param[out] none
    \retval     none
*/
void internal_delay_loop(uint32_t count)
{
    volatile uint32_t i;

    for(i = 0; i < count; i++) {
        __NOP();
    }
}

/*!
    \brief      延时精度测试函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
//void internal_delay_test(void)
//{
//    DEBUG("=== 延时函数测试 (72MHz) ===\r\n");
//    DEBUG("系统频率: %d Hz\r\n", SystemCoreClock);

//    DEBUG("测试1ms延时...\r\n");
//    internal_delay_ms(1);
//    DEBUG("1ms延时完成\r\n");

//    DEBUG("测试100us延时...\r\n");
//    internal_delay_us(100);
//    DEBUG("100us延时完成\r\n");

//    DEBUG("测试10us延时...\r\n");
//    internal_delay_us(10);
//    DEBUG("10us延时完成\r\n");

//    DEBUG("延时测试完成\r\n");
//}

