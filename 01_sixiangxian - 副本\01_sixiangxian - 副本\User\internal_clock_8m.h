/*!
    \file    internal_clock_8m.h
    \brief   简单的内部8MHz时钟配置头文件 for GD32F3x0
    
    \version 2025-01-01, V1.0.0, firmware for GD32F3x0
*/

#ifndef __INTERNAL_CLOCK_8M_H
#define __INTERNAL_CLOCK_8M_H

#include "gd32f3x0.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 函数声明 */

/* 基础时钟配置函数 */
void internal_clock_8m_init(void);
void internal_clock_max_freq_init(void);  /* 配置为最大频率108MHz */

/* 延时函数 */
void delay_ms(uint32_t ms);          /* 主要延时函数（基于SysTick） */
void delay_us(uint32_t us);
void internal_delay_ms_systick(uint32_t ms);  /* 基于SysTick的精确延时 */
void internal_delay_us_systick(uint32_t us);  /* 基于SysTick的精确延时 */
void internal_delay_ms_loop(uint32_t ms);     /* 基于循环的延时（备用） */
void internal_delay_us_loop(uint32_t us);     /* 基于循环的延时（备用） */

/* 获取当前频率 */
uint32_t internal_get_system_freq(void);

#ifdef __cplusplus
}
#endif

#endif /* __INTERNAL_CLOCK_8M_H */
